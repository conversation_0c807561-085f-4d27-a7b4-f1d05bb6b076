#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import os
import re
from datetime import datetime

class TemuJSAnalyzer:
    def __init__(self):
        # 禁用代理
        os.environ.pop('HTTP_PROXY', None)
        os.environ.pop('HTTPS_PROXY', None)
        os.environ.pop('http_proxy', None)
        os.environ.pop('https_proxy', None)
        
        self.session = requests.Session()
        proxies = {'http': None, 'https': None}
        self.session.proxies.update(proxies)
        self.session.trust_env = False
        
        # 设置cookie
        cookie_string = "api_uid=Ct4kjmiNlzU98QBGDn3oAg==; _nano_fp=Xpmyn0golpC8XqT8X9_v~JX55DvIt6xeNJtDofAJ; _bee=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; njrpl=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; dilx=MchfnouEwE7NM0Un4s5iO; hfsc=L3yPfIgw4Dbw15/MeQ==; seller_temp=N_eyJ0IjoiUXlzUVZhMHlIMTcxb1hFemhoUlpMWlA5dkVoUzJoRlFiSjZxRTJOYWVldnFjZWt1aGU2cTV4VWsrY0ZqUWdJU2t5dEI1RVJkUjdXT0xJc21rWmhjVHc9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyNDI3ODU0ODg1MTkwMn0=; mallid=634418224371052"
        
        # 解析cookie字符串
        for cookie in cookie_string.split('; '):
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                self.session.cookies.set(name, value, domain='.temu.com')
                self.session.cookies.set(name, value, domain='.kwcdn.com')
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Referer': 'https://agentseller.temu.com/',
            'Sec-Fetch-Dest': 'script',
            'Sec-Fetch-Mode': 'no-cors',
            'Sec-Fetch-Site': 'cross-site'
        })
        
        self.js_files = [
            "https://bstatic.kwcdn.com/static/temu-sca-container/342.2fefe74c.js",
            "https://bstatic.kwcdn.com/static/temu-sca-container/main.a9651195.js"
        ]
    
    def download_js_files(self):
        """下载JavaScript文件"""
        print(f"🔍 下载JavaScript文件...")
        
        js_contents = {}
        
        for js_url in self.js_files:
            print(f"  下载: {js_url}")
            
            try:
                response = self.session.get(js_url, timeout=30)
                
                if response.status_code == 200:
                    content = response.text
                    js_contents[js_url] = content
                    print(f"    ✅ 成功 ({len(content)} 字符)")
                    
                    # 保存到文件
                    filename = js_url.split('/')[-1]
                    with open(f"js_{filename}", 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"    💾 保存为: js_{filename}")
                    
                else:
                    print(f"    ❌ 失败 (状态码: {response.status_code})")
                    
            except Exception as e:
                print(f"    ❌ 错误: {str(e)}")
        
        return js_contents
    
    def extract_routes_from_js(self, js_content):
        """从JavaScript中提取路由"""
        print(f"🔍 从JavaScript中提取路由...")
        
        routes = set()
        
        # 各种路由模式
        route_patterns = [
            # React Router 模式
            r'path:\s*["\']([^"\']+)["\']',
            r'route:\s*["\']([^"\']+)["\']',
            r'to:\s*["\']([^"\']+)["\']',
            r'href:\s*["\']([^"\']+)["\']',
            
            # 字符串中的路径
            r'["\']([/][a-zA-Z0-9\-_/]+)["\']',
            
            # 路由配置
            r'routes?\s*:\s*\[([^\]]+)\]',
            r'children\s*:\s*\[([^\]]+)\]',
            
            # 导航相关
            r'navigate\(["\']([^"\']+)["\']',
            r'push\(["\']([^"\']+)["\']',
            r'replace\(["\']([^"\']+)["\']',
            
            # URL构建
            r'url\s*:\s*["\']([^"\']+)["\']',
            r'endpoint\s*:\s*["\']([^"\']+)["\']'
        ]
        
        for pattern in route_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, str) and match.startswith('/') and len(match) > 1:
                    # 过滤掉明显不是路由的路径
                    if not any(ext in match.lower() for ext in ['.js', '.css', '.png', '.jpg', '.ico', '.svg', '.woff', '.ttf']):
                        routes.add(match)
        
        print(f"  ✅ 提取到 {len(routes)} 个路由")
        return list(routes)
    
    def extract_api_endpoints_from_js(self, js_content):
        """从JavaScript中提取API端点"""
        print(f"🔍 从JavaScript中提取API端点...")
        
        api_endpoints = set()
        
        # API端点模式
        api_patterns = [
            # 直接的API路径
            r'["\']([/]api[/][^"\']+)["\']',
            
            # API配置
            r'api\s*:\s*["\']([^"\']+)["\']',
            r'endpoint\s*:\s*["\']([/]api[^"\']+)["\']',
            r'url\s*:\s*["\']([/]api[^"\']+)["\']',
            
            # HTTP请求
            r'fetch\(["\']([/]api[^"\']+)["\']',
            r'axios\.[a-z]+\(["\']([/]api[^"\']+)["\']',
            r'request\(["\']([/]api[^"\']+)["\']',
            
            # 服务调用
            r'service\.[a-zA-Z]+\(["\']([/]api[^"\']+)["\']',
            r'call\(["\']([/]api[^"\']+)["\']'
        ]
        
        for pattern in api_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, str) and match.startswith('/api/'):
                    api_endpoints.add(match)
        
        print(f"  ✅ 提取到 {len(api_endpoints)} 个API端点")
        return list(api_endpoints)
    
    def extract_menu_structure_from_js(self, js_content):
        """从JavaScript中提取菜单结构"""
        print(f"🔍 从JavaScript中提取菜单结构...")
        
        menu_items = []
        
        # 查找菜单相关的配置
        menu_patterns = [
            # 菜单配置对象
            r'menu\s*:\s*\[([^\]]+)\]',
            r'navigation\s*:\s*\[([^\]]+)\]',
            r'sidebar\s*:\s*\[([^\]]+)\]',
            
            # 菜单项
            r'title\s*:\s*["\']([^"\']+)["\']',
            r'label\s*:\s*["\']([^"\']+)["\']',
            r'name\s*:\s*["\']([^"\']+)["\']',
            
            # 图标和路径组合
            r'\{\s*icon\s*:[^,]+,\s*title\s*:\s*["\']([^"\']+)["\'][^}]*path\s*:\s*["\']([^"\']+)["\']',
            r'\{\s*path\s*:\s*["\']([^"\']+)["\'][^}]*title\s*:\s*["\']([^"\']+)["\']'
        ]
        
        for pattern in menu_patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    menu_items.append({
                        'title': match[0] if len(match) > 0 else '',
                        'path': match[1] if len(match) > 1 else ''
                    })
                elif isinstance(match, str) and len(match.strip()) > 2:
                    menu_items.append({'title': match.strip(), 'path': ''})
        
        print(f"  ✅ 提取到 {len(menu_items)} 个菜单项")
        return menu_items
    
    def extract_component_names_from_js(self, js_content):
        """从JavaScript中提取组件名称"""
        print(f"🔍 从JavaScript中提取组件名称...")
        
        components = set()
        
        # 组件模式
        component_patterns = [
            # React组件
            r'component\s*:\s*([A-Z][a-zA-Z0-9]+)',
            r'Component\s*=\s*([A-Z][a-zA-Z0-9]+)',
            
            # 懒加载组件
            r'lazy\(\s*\(\)\s*=>\s*import\(["\']([^"\']+)["\']',
            
            # 组件导入
            r'import\s+([A-Z][a-zA-Z0-9]+)\s+from',
            r'import\s*\{\s*([A-Z][a-zA-Z0-9]+)\s*\}',
            
            # 页面组件
            r'([A-Z][a-zA-Z0-9]*Page)',
            r'([A-Z][a-zA-Z0-9]*View)',
            r'([A-Z][a-zA-Z0-9]*Screen)'
        ]
        
        for pattern in component_patterns:
            matches = re.findall(pattern, js_content)
            for match in matches:
                if isinstance(match, str) and len(match) > 2:
                    components.add(match)
        
        print(f"  ✅ 提取到 {len(components)} 个组件")
        return list(components)
    
    def test_discovered_routes(self, routes):
        """测试发现的路由"""
        print(f"🔍 测试发现的路由...")
        
        accessible_routes = []
        base_url = "https://agentseller.temu.com"
        
        # 设置页面请求头
        page_headers = self.session.headers.copy()
        page_headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate'
        })
        
        for route in sorted(set(routes)):
            if len(route) > 50:  # 跳过过长的路径
                continue
                
            url = f"{base_url}{route}"
            print(f"  测试: {route}")
            
            try:
                response = self.session.get(url, headers=page_headers, timeout=10)
                
                if response.status_code == 200:
                    print(f"    ✅ 可访问")
                    accessible_routes.append({
                        'route': route,
                        'url': url,
                        'status_code': response.status_code
                    })
                elif response.status_code in [301, 302]:
                    location = response.headers.get('location', '')
                    print(f"    🔄 重定向到: {location}")
                elif response.status_code == 404:
                    print(f"    ❌ 未找到")
                else:
                    print(f"    ⚠️ 状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"    ❌ 错误: {str(e)}")
        
        return accessible_routes
    
    def test_discovered_apis(self, api_endpoints):
        """测试发现的API端点"""
        print(f"🔍 测试发现的API端点...")
        
        working_apis = []
        base_url = "https://agentseller.temu.com"
        
        # 设置API请求头
        api_headers = self.session.headers.copy()
        api_headers.update({
            'Accept': '*/*',
            'Content-Type': 'application/json',
            'Referer': 'https://agentseller.temu.com/',
            'anti-content': '0aqAfxnYsyPdJgd9Q2I8qlOivTg2VWUwZNpNM3Hc8XcXvoD10n6YmtiazyAOihcFiOvnaiZsdwfnts96iq_CZoPp9t0ZZ2rY0ba41PQgQHTAZUPV-kB6k7MZQNWWKaq8CWlReXtZOKdj0e0vH-qme1kI_FO7_PMZqpyopUPTRZ9maP-YAuDe-9kWD83XJa4xyvFzglM1FkCdXtbmCytLPQthBX4hE0aTJpwNwOASpmux89n67EHo_KLqFBBN3uKLZWNC4tL9POo6rYGGj9A6EuKzgB6b2kX5nGdGqPBui9hK9_qL0O_V0WSk8pIVZQoWEx-3HCFl6Ys3xdscPCRfTnos7y4qH2Oi0g32E8TiEY8SIHjvIild8cmyTc_UGwOtrl7bR_gEOCewuldnBHIACNDnJ7ldQKXCrQ_e-uQJtr01VtJ1AJ7jfunRwJGqKEkTw9NCOmONefDNhFsp3yTGJSsoAJ6L8XAq9uZiiLjpHC3nuunH-krjki1-HxfGXRblF_5YRqGXfgSP89HJN2CocuOEGPRDxYpH4anLv4lOkUjfJI1zERQmDzywnHQrBgYnkdoqdSNeuExozpky2vlNgCHhJLEoJt7MsD8OcwuXdhQB7q4ytxCslcl_568YubpbeCSVMKDDvizZEpOUnUv4f8ir3A08088_3uvC3nrr4pzyGVH1LEac5nSAyQ6upzZiw8a_bWjnbmX5aFPn0DA1fL1BskDuH8Qd7N96orM9k7dPXH8',
            'mallid': '634418224371052'
        })
        
        for api in sorted(set(api_endpoints)):
            url = f"{base_url}{api}"
            print(f"  测试API: {api}")
            
            # 尝试GET和POST
            for method in ['GET', 'POST']:
                try:
                    if method == 'GET':
                        response = self.session.get(url, headers=api_headers, timeout=10)
                    else:
                        response = self.session.post(url, headers=api_headers, json={}, timeout=10)
                    
                    if response.status_code == 200:
                        content_type = response.headers.get('content-type', '')
                        if 'application/json' in content_type:
                            try:
                                data = response.json()
                                print(f"    ✅ {method}成功 (JSON)")
                                working_apis.append({
                                    'endpoint': api,
                                    'method': method,
                                    'status_code': response.status_code,
                                    'response_type': 'JSON',
                                    'sample_response': str(data)[:200] + '...' if len(str(data)) > 200 else str(data)
                                })
                                break  # 成功了就不用测试其他方法
                            except:
                                print(f"    ✅ {method}成功 (非JSON)")
                        break  # 成功了就不用测试其他方法
                    elif response.status_code == 405 and method == 'GET':
                        continue  # 尝试POST
                    else:
                        if method == 'POST':  # 只在POST失败时报告
                            print(f"    ❌ 失败 ({response.status_code})")
                        
                except Exception as e:
                    if method == 'POST':  # 只在POST失败时报告
                        print(f"    ❌ 错误: {str(e)}")
        
        return working_apis
    
    def save_analysis_results(self, routes, apis, menu_items, components, accessible_routes, working_apis):
        """保存分析结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        analysis_results = {
            'timestamp': timestamp,
            'discovered_routes': routes,
            'discovered_apis': apis,
            'menu_items': menu_items,
            'components': components,
            'accessible_routes': accessible_routes,
            'working_apis': working_apis,
            'summary': {
                'total_routes_discovered': len(routes),
                'total_apis_discovered': len(apis),
                'total_menu_items': len(menu_items),
                'total_components': len(components),
                'accessible_routes_count': len(accessible_routes),
                'working_apis_count': len(working_apis)
            }
        }
        
        filename = f"temu_js_analysis_{timestamp}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(analysis_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 JavaScript分析结果已保存: {filename}")
        return filename

def main():
    print("🚀 Temu JavaScript文件分析工具")
    print("🔍 深入分析前端代码发现路由和API")
    
    analyzer = TemuJSAnalyzer()
    
    # 1. 下载JavaScript文件
    js_contents = analyzer.download_js_files()
    
    if not js_contents:
        print("❌ 无法下载JavaScript文件")
        return
    
    # 2. 分析所有JavaScript内容
    all_routes = []
    all_apis = []
    all_menu_items = []
    all_components = []
    
    for js_url, js_content in js_contents.items():
        print(f"\n🔍 分析文件: {js_url}")
        
        routes = analyzer.extract_routes_from_js(js_content)
        apis = analyzer.extract_api_endpoints_from_js(js_content)
        menu_items = analyzer.extract_menu_structure_from_js(js_content)
        components = analyzer.extract_component_names_from_js(js_content)
        
        all_routes.extend(routes)
        all_apis.extend(apis)
        all_menu_items.extend(menu_items)
        all_components.extend(components)
    
    # 去重
    all_routes = list(set(all_routes))
    all_apis = list(set(all_apis))
    all_components = list(set(all_components))
    
    print(f"\n📊 分析总结:")
    print(f"   发现路由: {len(all_routes)}")
    print(f"   发现API: {len(all_apis)}")
    print(f"   发现菜单项: {len(all_menu_items)}")
    print(f"   发现组件: {len(all_components)}")
    
    # 3. 测试发现的路由和API
    accessible_routes = analyzer.test_discovered_routes(all_routes)
    working_apis = analyzer.test_discovered_apis(all_apis)
    
    # 4. 保存结果
    filename = analyzer.save_analysis_results(
        all_routes, all_apis, all_menu_items, all_components,
        accessible_routes, working_apis
    )
    
    print(f"\n🎉 JavaScript分析完成!")
    print(f"   可访问路由: {len(accessible_routes)}")
    print(f"   工作中的API: {len(working_apis)}")
    print(f"   结果文件: {filename}")

if __name__ == "__main__":
    main()
