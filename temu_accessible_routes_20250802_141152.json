[{"route": "/dashboard", "url": "https://agentseller.temu.com/dashboard", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/home", "url": "https://agentseller.temu.com/home", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/overview", "url": "https://agentseller.temu.com/overview", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/summary", "url": "https://agentseller.temu.com/summary", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/products", "url": "https://agentseller.temu.com/products", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/product", "url": "https://agentseller.temu.com/product", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/goods", "url": "https://agentseller.temu.com/goods", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/items", "url": "https://agentseller.temu.com/items", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/orders", "url": "https://agentseller.temu.com/orders", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/order", "url": "https://agentseller.temu.com/order", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/sales", "url": "https://agentseller.temu.com/sales", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/transactions", "url": "https://agentseller.temu.com/transactions", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/inventory", "url": "https://agentseller.temu.com/inventory", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/stock", "url": "https://agentseller.temu.com/stock", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/warehouse", "url": "https://agentseller.temu.com/warehouse", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/analytics", "url": "https://agentseller.temu.com/analytics", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/reports", "url": "https://agentseller.temu.com/reports", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/statistics", "url": "https://agentseller.temu.com/statistics", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/data", "url": "https://agentseller.temu.com/data", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/settings", "url": "https://agentseller.temu.com/settings", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/config", "url": "https://agentseller.temu.com/config", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/profile", "url": "https://agentseller.temu.com/profile", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/account", "url": "https://agentseller.temu.com/account", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/finance", "url": "https://agentseller.temu.com/finance", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/payment", "url": "https://agentseller.temu.com/payment", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/billing", "url": "https://agentseller.temu.com/billing", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/revenue", "url": "https://agentseller.temu.com/revenue", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/shipping", "url": "https://agentseller.temu.com/shipping", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/logistics", "url": "https://agentseller.temu.com/logistics", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/delivery", "url": "https://agentseller.temu.com/delivery", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/customers", "url": "https://agentseller.temu.com/customers", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/buyers", "url": "https://agentseller.temu.com/buyers", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/users", "url": "https://agentseller.temu.com/users", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/marketing", "url": "https://agentseller.temu.com/marketing", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/promotion", "url": "https://agentseller.temu.com/promotion", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/ads", "url": "https://agentseller.temu.com/ads", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/campaign", "url": "https://agentseller.temu.com/campaign", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/support", "url": "https://agentseller.temu.com/support", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/help", "url": "https://agentseller.temu.com/help", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/service", "url": "https://agentseller.temu.com/service", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/chat", "url": "https://agentseller.temu.com/chat", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/newon", "url": "https://agentseller.temu.com/newon", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/semi", "url": "https://agentseller.temu.com/semi", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/supplier", "url": "https://agentseller.temu.com/supplier", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/sourcing", "url": "https://agentseller.temu.com/sourcing", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/product-select", "url": "https://agentseller.temu.com/product-select", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/product-management", "url": "https://agentseller.temu.com/product-management", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/order-management", "url": "https://agentseller.temu.com/order-management", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/inventory-management", "url": "https://agentseller.temu.com/inventory-management", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/data-analysis", "url": "https://agentseller.temu.com/data-analysis", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/business-intelligence", "url": "https://agentseller.temu.com/business-intelligence", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/seller-center", "url": "https://agentseller.temu.com/seller-center", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/merchant", "url": "https://agentseller.temu.com/merchant", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/vendor", "url": "https://agentseller.temu.com/vendor", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/main/dashboard", "url": "https://agentseller.temu.com/main/dashboard", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/main/products", "url": "https://agentseller.temu.com/main/products", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/main/orders", "url": "https://agentseller.temu.com/main/orders", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/main/analytics", "url": "https://agentseller.temu.com/main/analytics", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/main/settings", "url": "https://agentseller.temu.com/main/settings", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/main/finance", "url": "https://agentseller.temu.com/main/finance", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/newon/product-select", "url": "https://agentseller.temu.com/newon/product-select", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/newon/sourcing", "url": "https://agentseller.temu.com/newon/sourcing", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/semi/products", "url": "https://agentseller.temu.com/semi/products", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/semi/orders", "url": "https://agentseller.temu.com/semi/orders", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/semi/analytics", "url": "https://agentseller.temu.com/semi/analytics", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/lgst/shipping", "url": "https://agentseller.temu.com/lgst/shipping", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/lgst/tracking", "url": "https://agentseller.temu.com/lgst/tracking", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/lgst/warehouse", "url": "https://agentseller.temu.com/lgst/warehouse", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/labor/management", "url": "https://agentseller.temu.com/labor/management", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/labor/analytics", "url": "https://agentseller.temu.com/labor/analytics", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/stock/inventory", "url": "https://agentseller.temu.com/stock/inventory", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/stock/management", "url": "https://agentseller.temu.com/stock/management", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/chat-app/messages", "url": "https://agentseller.temu.com/chat-app/messages", "status_code": 200, "title": "<PERSON><PERSON> Central"}, {"route": "/chat-app/support", "url": "https://agentseller.temu.com/chat-app/support", "status_code": 200, "title": "<PERSON><PERSON> Central"}]