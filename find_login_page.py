#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import re
import os
from urllib.parse import urljoin, urlparse

def find_login_page():
    """寻找真正的登录页面"""
    
    # 禁用代理
    os.environ.pop('HTTP_PROXY', None)
    os.environ.pop('HTTPS_PROXY', None)
    os.environ.pop('http_proxy', None)
    os.environ.pop('https_proxy', None)
    
    session = requests.Session()
    proxies = {'http': None, 'https': None}
    session.proxies.update(proxies)
    session.trust_env = False
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    session.headers.update(headers)
    
    # 尝试不同的登录页面路径
    login_paths = [
        'https://seller.temu.com/login',
        'https://seller.temu.com/auth/login',
        'https://seller.temu.com/signin',
        'https://seller.temu.com/w/login',
        'https://seller.temu.com/w/auth',
        'https://seller.temu.com/w/signin',
        'https://agentseller.temu.com/login',
        'https://agentseller.temu.com/auth/login',
        'https://agentseller.temu.com/signin',
        'https://login.temu.com/',
        'https://auth.temu.com/',
        'https://passport.temu.com/',
        'https://account.temu.com/login'
    ]
    
    print("正在尝试不同的登录页面路径...")
    
    for path in login_paths:
        try:
            print(f"\n尝试访问: {path}")
            response = session.get(path, timeout=30, allow_redirects=True)
            print(f"状态码: {response.status_code}")
            print(f"最终URL: {response.url}")
            
            if response.status_code == 200:
                content = response.text
                print(f"页面内容长度: {len(content)}")
                
                # 检查是否包含登录表单
                login_indicators = [
                    'password',
                    'username',
                    'email',
                    'phone',
                    'login',
                    'signin',
                    'auth',
                    'form',
                    'input'
                ]
                
                found_indicators = []
                for indicator in login_indicators:
                    if indicator.lower() in content.lower():
                        found_indicators.append(indicator)
                
                if found_indicators:
                    print(f"找到登录相关内容: {found_indicators}")
                    
                    # 保存页面内容
                    filename = f"login_page_{path.replace('https://', '').replace('/', '_')}.html"
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"页面内容已保存到: {filename}")
                    
                    # 查找表单和API端点
                    form_pattern = r'<form[^>]*action=["\']([^"\']*)["\'][^>]*>'
                    forms = re.findall(form_pattern, content, re.IGNORECASE)
                    if forms:
                        print(f"找到表单action: {forms}")
                    
                    # 查找API端点
                    api_patterns = [
                        r'["\']https?://[^"\']*api[^"\']*login[^"\']*["\']',
                        r'["\']https?://[^"\']*login[^"\']*api[^"\']*["\']',
                        r'["\']https?://[^"\']*auth[^"\']*["\']',
                        r'["\']\/api\/[^"\']*login[^"\']*["\']',
                        r'["\']\/api\/[^"\']*auth[^"\']*["\']'
                    ]
                    
                    found_apis = []
                    for pattern in api_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        found_apis.extend([match.strip('"\'') for match in matches])
                    
                    if found_apis:
                        print(f"找到API端点: {set(found_apis)}")
                    
                    return path, content
                else:
                    print("未找到登录相关内容")
            
            elif response.status_code == 302 or response.status_code == 301:
                print(f"重定向到: {response.headers.get('Location', 'Unknown')}")
            
            else:
                print(f"其他状态码: {response.status_code}")
                
        except Exception as e:
            print(f"访问失败: {str(e)}")
    
    print("\n未找到有效的登录页面")
    return None, None

if __name__ == "__main__":
    login_url, content = find_login_page()
    if login_url:
        print(f"\n✅ 找到登录页面: {login_url}")
    else:
        print("\n❌ 未找到登录页面")
