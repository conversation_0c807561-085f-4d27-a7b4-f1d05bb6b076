#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import os
import time
from datetime import datetime

class AgentsellerAPIExplorer:
    def __init__(self):
        # 禁用代理
        os.environ.pop('HTTP_PROXY', None)
        os.environ.pop('HTTPS_PROXY', None)
        os.environ.pop('http_proxy', None)
        os.environ.pop('https_proxy', None)
        
        self.session = requests.Session()
        proxies = {'http': None, 'https': None}
        self.session.proxies.update(proxies)
        self.session.trust_env = False
        
        # 设置cookie
        cookie_string = "api_uid=Ct4kjmiNlzU98QBGDn3oAg==; _nano_fp=Xpmyn0golpC8XqT8X9_v~JX55DvIt6xeNJtDofAJ; _bee=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; njrpl=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; dilx=MchfnouEwE7NM0Un4s5iO; hfsc=L3yPfIgw4Dbw15/MeQ==; seller_temp=N_eyJ0IjoiUXlzUVZhMHlIMTcxb1hFemhoUlpMWlA5dkVoUzJoRlFiSjZxRTJOYWVldnFjZWt1aGU2cTV4VWsrY0ZqUWdJU2t5dEI1RVJkUjdXT0xJc21rWmhjVHc9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyNDI3ODU0ODg1MTkwMn0=; mallid=634418224371052"
        
        # 解析cookie字符串
        for cookie in cookie_string.split('; '):
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                self.session.cookies.set(name, value, domain='.temu.com')
                self.session.cookies.set(name, value, domain='.agentseller.temu.com')
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://agentseller.temu.com/',
            'Origin': 'https://agentseller.temu.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Content-Type': 'application/json'
        })
        
        self.base_url = 'https://agentseller.temu.com'
        self.successful_apis = []
    
    def test_api(self, endpoint, data=None):
        """测试单个API端点"""
        try:
            url = f"{self.base_url}{endpoint}"
            response = self.session.post(url, json=data or {}, timeout=30)
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                if 'application/json' in content_type:
                    try:
                        result = response.json()
                        return True, result
                    except json.JSONDecodeError:
                        return False, "Invalid JSON"
            
            return False, f"Status: {response.status_code}"
            
        except Exception as e:
            return False, str(e)
    
    def explore_quick_apis(self):
        """探索/quick路径下的API"""
        print("🔍 探索 /quick 路径下的API...")
        
        quick_endpoints = [
            '/quick/merchant/pop/query',  # 已知有效
            '/quick/merchant/info',
            '/quick/merchant/profile',
            '/quick/merchant/dashboard',
            '/quick/merchant/stats',
            '/quick/merchant/summary',
            '/quick/store/info',
            '/quick/store/profile',
            '/quick/store/dashboard',
            '/quick/store/stats',
            '/quick/product/list',
            '/quick/product/info',
            '/quick/product/summary',
            '/quick/order/list',
            '/quick/order/info',
            '/quick/order/summary',
            '/quick/order/stats',
            '/quick/sales/summary',
            '/quick/sales/stats',
            '/quick/analytics/overview',
            '/quick/analytics/summary',
            '/quick/account/info',
            '/quick/account/profile',
            '/quick/notification/list',
            '/quick/message/list'
        ]
        
        for endpoint in quick_endpoints:
            print(f"  测试: {endpoint}")
            success, result = self.test_api(endpoint)
            
            if success:
                print(f"    ✅ 成功!")
                print(f"    响应: {json.dumps(result, ensure_ascii=False, indent=6)}")
                self.successful_apis.append({
                    'endpoint': endpoint,
                    'data': result,
                    'timestamp': datetime.now().isoformat()
                })
            else:
                print(f"    ❌ 失败: {result}")
            
            time.sleep(0.1)  # 避免请求过快
    
    def explore_with_pagination(self):
        """使用分页参数探索API"""
        print("\n📄 使用分页参数探索API...")
        
        list_endpoints = [
            '/quick/product/list',
            '/quick/order/list',
            '/quick/notification/list',
            '/quick/message/list'
        ]
        
        pagination_params = [
            {'page': 1, 'size': 10},
            {'pageNum': 1, 'pageSize': 10},
            {'offset': 0, 'limit': 10},
            {'start': 0, 'count': 10},
            {'current': 1, 'pageSize': 10}
        ]
        
        for endpoint in list_endpoints:
            print(f"  测试分页: {endpoint}")
            
            for params in pagination_params:
                success, result = self.test_api(endpoint, params)
                if success:
                    print(f"    ✅ 分页成功! 参数: {params}")
                    print(f"    响应: {json.dumps(result, ensure_ascii=False, indent=6)}")
                    self.successful_apis.append({
                        'endpoint': endpoint,
                        'params': params,
                        'data': result,
                        'timestamp': datetime.now().isoformat()
                    })
                    break  # 找到有效参数就停止
            
            time.sleep(0.1)
    
    def explore_api_bg_ladyfish(self):
        """探索/api/bg-ladyfish路径下的API"""
        print("\n🐟 探索 /api/bg-ladyfish 路径下的API...")
        
        # 基于已知的路径模式
        bg_endpoints = [
            '/api/bg-ladyfish/mms/account/info',
            '/api/bg-ladyfish/mms/account/profile',
            '/api/bg-ladyfish/mms/store/info',
            '/api/bg-ladyfish/mms/store/profile',
            '/api/bg-ladyfish/mms/product/list',
            '/api/bg-ladyfish/mms/product/info',
            '/api/bg-ladyfish/mms/order/list',
            '/api/bg-ladyfish/mms/order/info',
            '/api/bg-ladyfish/mms/menu/list',
            '/api/bg-ladyfish/mms/menu/info',
            '/api/bg-ladyfish/mms/dashboard/data',
            '/api/bg-ladyfish/mms/analytics/summary',
            '/api/bg-ladyfish/mms/notification/list',
            '/api/bg-ladyfish/mms/message/list'
        ]
        
        for endpoint in bg_endpoints:
            print(f"  测试: {endpoint}")
            
            # 尝试GET请求
            try:
                url = f"{self.base_url}{endpoint}"
                response = self.session.get(url, timeout=30)
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'application/json' in content_type:
                        try:
                            result = response.json()
                            print(f"    ✅ GET成功!")
                            print(f"    响应: {json.dumps(result, ensure_ascii=False, indent=6)}")
                            self.successful_apis.append({
                                'endpoint': endpoint,
                                'method': 'GET',
                                'data': result,
                                'timestamp': datetime.now().isoformat()
                            })
                            continue
                        except json.JSONDecodeError:
                            pass
            except:
                pass
            
            # 尝试POST请求
            success, result = self.test_api(endpoint)
            if success:
                print(f"    ✅ POST成功!")
                print(f"    响应: {json.dumps(result, ensure_ascii=False, indent=6)}")
                self.successful_apis.append({
                    'endpoint': endpoint,
                    'method': 'POST',
                    'data': result,
                    'timestamp': datetime.now().isoformat()
                })
            else:
                print(f"    ❌ 失败: {result}")
            
            time.sleep(0.1)
    
    def try_common_data_apis(self):
        """尝试常见的数据API"""
        print("\n📊 尝试常见的数据API...")
        
        common_endpoints = [
            '/api/account/info',
            '/api/user/info',
            '/api/store/info',
            '/api/product/list',
            '/api/order/list',
            '/api/dashboard/data',
            '/api/analytics/summary',
            '/quick/dashboard',
            '/quick/overview',
            '/quick/summary'
        ]
        
        for endpoint in common_endpoints:
            print(f"  测试: {endpoint}")
            success, result = self.test_api(endpoint)
            
            if success:
                print(f"    ✅ 成功!")
                print(f"    响应: {json.dumps(result, ensure_ascii=False, indent=6)}")
                self.successful_apis.append({
                    'endpoint': endpoint,
                    'data': result,
                    'timestamp': datetime.now().isoformat()
                })
            else:
                print(f"    ❌ 失败: {result}")
            
            time.sleep(0.1)
    
    def save_results(self):
        """保存结果"""
        if not self.successful_apis:
            print("\n❌ 没有发现新的有效API")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"agentseller_apis_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.successful_apis, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 结果已保存到: {filename}")
        
        # 创建摘要
        summary = {
            'total_apis': len(self.successful_apis),
            'endpoints': [api['endpoint'] for api in self.successful_apis],
            'timestamp': datetime.now().isoformat()
        }
        
        summary_filename = f"agentseller_summary_{timestamp}.json"
        with open(summary_filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"📋 摘要已保存到: {summary_filename}")
        
        return filename, summary_filename

def main():
    print("🚀 开始深度探索 agentseller.temu.com API...")
    
    explorer = AgentsellerAPIExplorer()
    
    # 探索不同类型的API
    explorer.explore_quick_apis()
    explorer.explore_with_pagination()
    explorer.explore_api_bg_ladyfish()
    explorer.try_common_data_apis()
    
    # 保存结果
    if explorer.successful_apis:
        data_file, summary_file = explorer.save_results()
        
        print(f"\n🎉 探索完成!")
        print(f"发现 {len(explorer.successful_apis)} 个有效的API端点:")
        
        for api in explorer.successful_apis:
            endpoint = api['endpoint']
            method = api.get('method', 'POST')
            params = api.get('params', '')
            print(f"  ✅ {method} {endpoint} {params}")
        
        print(f"\n📁 文件:")
        print(f"  数据: {data_file}")
        print(f"  摘要: {summary_file}")
    else:
        print("\n❌ 未发现新的有效API端点")

if __name__ == "__main__":
    main()
