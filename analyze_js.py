#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import re
import os

def analyze_js_files():
    """分析JavaScript文件，寻找登录API"""
    
    # 禁用代理
    os.environ.pop('HTTP_PROXY', None)
    os.environ.pop('HTTPS_PROXY', None)
    os.environ.pop('http_proxy', None)
    os.environ.pop('https_proxy', None)
    
    session = requests.Session()
    proxies = {'http': None, 'https': None}
    session.proxies.update(proxies)
    session.trust_env = False
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
    }
    session.headers.update(headers)
    
    # 要分析的JavaScript文件
    js_files = [
        'https://static.kwcdn.com/m-assets/assets/js/no-auth_3e41245f962fd2c0.js',
        'https://static.kwcdn.com/m-assets/assets/js/vendors_be97743d63273664.js',
        'https://static.kwcdn.com/m-assets/assets/js/biz_vendors_d61465ef9f555270.js'
    ]
    
    for js_url in js_files:
        try:
            print(f"\n正在分析: {js_url}")
            response = session.get(js_url, timeout=30)
            
            if response.status_code == 200:
                js_content = response.text
                print(f"文件大小: {len(js_content)} 字符")
                
                # 查找API端点相关的模式
                api_patterns = [
                    r'["\']https?://[^"\']*api[^"\']*["\']',
                    r'["\']\/api\/[^"\']*["\']',
                    r'["\']\/bg\/[^"\']*["\']',
                    r'["\']https?://[^"\']*login[^"\']*["\']',
                    r'["\']https?://[^"\']*auth[^"\']*["\']',
                    r'["\']https?://[^"\']*signin[^"\']*["\']'
                ]
                
                found_apis = set()
                for pattern in api_patterns:
                    matches = re.findall(pattern, js_content, re.IGNORECASE)
                    for match in matches:
                        clean_match = match.strip('"\'')
                        if len(clean_match) > 5:  # 过滤太短的匹配
                            found_apis.add(clean_match)
                
                if found_apis:
                    print(f"找到的API端点:")
                    for api in sorted(found_apis):
                        print(f"  - {api}")
                
                # 查找登录相关的函数和变量
                login_patterns = [
                    r'login[A-Za-z]*\s*[:=]\s*["\'][^"\']*["\']',
                    r'auth[A-Za-z]*\s*[:=]\s*["\'][^"\']*["\']',
                    r'signin[A-Za-z]*\s*[:=]\s*["\'][^"\']*["\']',
                    r'LOGIN[A-Z_]*\s*[:=]\s*["\'][^"\']*["\']',
                    r'AUTH[A-Z_]*\s*[:=]\s*["\'][^"\']*["\']'
                ]
                
                found_login_vars = set()
                for pattern in login_patterns:
                    matches = re.findall(pattern, js_content, re.IGNORECASE)
                    found_login_vars.update(matches)
                
                if found_login_vars:
                    print(f"找到的登录相关变量:")
                    for var in sorted(found_login_vars):
                        print(f"  - {var}")
                
                # 查找可能的API基础URL
                base_url_patterns = [
                    r'baseURL\s*[:=]\s*["\'][^"\']*["\']',
                    r'apiUrl\s*[:=]\s*["\'][^"\']*["\']',
                    r'API_BASE\s*[:=]\s*["\'][^"\']*["\']',
                    r'BASE_URL\s*[:=]\s*["\'][^"\']*["\']'
                ]
                
                found_base_urls = set()
                for pattern in base_url_patterns:
                    matches = re.findall(pattern, js_content, re.IGNORECASE)
                    found_base_urls.update(matches)
                
                if found_base_urls:
                    print(f"找到的基础URL:")
                    for url in sorted(found_base_urls):
                        print(f"  - {url}")
                
                # 查找POST请求相关的代码
                post_patterns = [
                    r'\.post\s*\(\s*["\'][^"\']*["\']',
                    r'method\s*:\s*["\']POST["\']',
                    r'fetch\s*\(\s*["\'][^"\']*["\'][^)]*method[^)]*POST'
                ]
                
                found_posts = set()
                for pattern in post_patterns:
                    matches = re.findall(pattern, js_content, re.IGNORECASE)
                    found_posts.update(matches)
                
                if found_posts:
                    print(f"找到的POST请求:")
                    for post in sorted(found_posts):
                        print(f"  - {post}")
                
                # 保存文件内容以便进一步分析
                filename = js_url.split('/')[-1]
                with open(f"js_{filename}", 'w', encoding='utf-8') as f:
                    f.write(js_content)
                print(f"JavaScript文件已保存到: js_{filename}")
                
            else:
                print(f"下载失败，状态码: {response.status_code}")
                
        except Exception as e:
            print(f"分析失败: {str(e)}")

if __name__ == "__main__":
    analyze_js_files()
