#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import os
from datetime import datetime

class AntiContentAPITester:
    def __init__(self):
        # 禁用代理
        os.environ.pop('HTTP_PROXY', None)
        os.environ.pop('HTTPS_PROXY', None)
        os.environ.pop('http_proxy', None)
        os.environ.pop('https_proxy', None)
        
        self.session = requests.Session()
        proxies = {'http': None, 'https': None}
        self.session.proxies.update(proxies)
        self.session.trust_env = False
        
        # 设置cookie
        cookie_string = "api_uid=Ct4kjmiNlzU98QBGDn3oAg==; _nano_fp=Xpmyn0golpC8XqT8X9_v~JX55DvIt6xeNJtDofAJ; _bee=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; njrpl=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; dilx=MchfnouEwE7NM0Un4s5iO; hfsc=L3yPfIgw4Dbw15/MeQ==; seller_temp=N_eyJ0IjoiUXlzUVZhMHlIMTcxb1hFemhoUlpMWlA5dkVoUzJoRlFiSjZxRTJOYWVldnFjZWt1aGU2cTV4VWsrY0ZqUWdJU2t5dEI1RVJkUjdXT0xJc21rWmhjVHc9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyNDI3ODU0ODg1MTkwMn0=; mallid=634418224371052"
        
        # 解析cookie字符串
        for cookie in cookie_string.split('; '):
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                self.session.cookies.set(name, value, domain='.temu.com')
                self.session.cookies.set(name, value, domain='.agentseller.temu.com')
        
        # 设置完整的请求头 - 包含关键的anti-content头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Referer': 'https://agentseller.temu.com/newon/product-select',
            'Origin': 'https://agentseller.temu.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Priority': 'u=1, i',
            'Cache-Control': 'max-age=0',
            'Content-Type': 'application/json',
            'mallid': '634418224371052',
            # 关键的anti-content头部
            'anti-content': '0aqAfxnYsyPdJgd9Q2I8qlOivTg2VWUwZNpNM3Hc8XcXvoD10n6YmtiazyAOihcFiOvnaiZsdwfnts96iq_CZoPp9t0ZZ2rY0ba41PQgQHTAZUPV-kB6k7MZQNWWKaq8CWlReXtZOKdj0e0vH-qme1kI_FO7_PMZqpyopUPTRZ9maP-YAuDe-9kWD83XJa4xyvFzglM1FkCdXtbmCytLPQthBX4hE0aTJpwNwOASpmux89n67EHo_KLqFBBN3uKLZWNC4tL9POo6rYGGj9A6EuKzgB6b2kX5nGdGqPBui9hK9_qL0O_V0WSk8pIVZQoWEx-3HCFl6Ys3xdscPCRfTnos7y4qH2Oi0g32E8TiEY8SIHjvIild8cmyTc_UGwOtrl7bR_gEOCewuldnBHIACNDnJ7ldQKXCrQ_e-uQJtr01VtJ1AJ7jfunRwJGqKEkTw9NCOmONefDNhFsp3yTGJSsoAJ6L8XAq9uZiiLjpHC3nuunH-krjki1-HxfGXRblF_5YRqGXfgSP89HJN2CocuOEGPRDxYpH4anLv4lOkUjfJI1zERQmDzywnHQrBgYnkdoqdSNeuExozpky2vlNgCHhJLEoJt7MsD8OcwuXdhQB7q4ytxCslcl_568YubpbeCSVMKDDvizZEpOUnUv4f8ir3A08088_3uvC3nrr4pzyGVH1LEac5nSAyQ6upzZiw8a_bWjnbmX5aFPn0DA1fL1BskDuH8Qd7N96orM9k7dPXH8'
        })
    
    def test_kiana_api_with_anti_content(self):
        """使用anti-content头测试Kiana API"""
        url = "https://agentseller.temu.com/api/kiana/mms/robin/searchForSemiSupplier"
        
        print(f"🔍 使用完整请求头测试API: {url}")
        print(f"🔑 包含anti-content头部")
        
        # 你能告诉我浏览器中实际发送的POST数据是什么吗？
        # 先尝试一些常见的参数格式
        test_cases = [
            # 空参数
            {},
            
            # 基础搜索参数
            {'keyword': ''},
            {'searchText': ''},
            {'query': ''},
            
            # 分页参数
            {'page': 1, 'size': 10},
            {'pageNum': 1, 'pageSize': 10},
            {'current': 1, 'pageSize': 10},
            
            # 供应商相关参数
            {'supplierType': 'semi'},
            {'type': 'semi'},
            {'category': ''},
            
            # 可能的完整参数结构
            {
                'keyword': '',
                'page': 1,
                'size': 10,
                'supplierType': 'semi',
                'category': '',
                'sort': 'default'
            },
            {
                'searchText': '',
                'pageNum': 1,
                'pageSize': 10,
                'type': 'semi',
                'filters': []
            }
        ]
        
        successful_calls = []
        
        for i, data in enumerate(test_cases):
            print(f"\n  测试 {i+1}: POST 请求")
            print(f"    参数: {json.dumps(data, ensure_ascii=False)}")
            
            try:
                response = self.session.post(url, json=data, timeout=30)
                
                print(f"    状态码: {response.status_code}")
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    print(f"    Content-Type: {content_type}")
                    
                    if 'application/json' in content_type:
                        try:
                            result = response.json()
                            print(f"    ✅ JSON响应成功!")
                            print(f"    响应数据: {json.dumps(result, ensure_ascii=False, indent=6)}")
                            
                            successful_calls.append({
                                'endpoint': '/api/kiana/mms/robin/searchForSemiSupplier',
                                'method': 'POST',
                                'data': data,
                                'response': result,
                                'timestamp': datetime.now().isoformat()
                            })
                            
                        except json.JSONDecodeError:
                            print(f"    ⚠️ 响应不是有效JSON")
                            print(f"    响应内容: {response.text[:500]}")
                    else:
                        print(f"    ⚠️ 非JSON响应")
                        print(f"    响应内容: {response.text[:200]}")
                
                elif response.status_code == 400:
                    print(f"    ⚠️ 请求错误 (400)")
                    try:
                        error = response.json()
                        print(f"    错误信息: {json.dumps(error, ensure_ascii=False, indent=6)}")
                    except:
                        print(f"    错误内容: {response.text[:200]}")
                
                elif response.status_code == 403:
                    print(f"    🚫 权限不足 (403)")
                    try:
                        error = response.json()
                        print(f"    错误信息: {json.dumps(error, ensure_ascii=False, indent=6)}")
                    except:
                        print(f"    错误内容: {response.text[:200]}")
                
                else:
                    print(f"    ⚠️ 状态码: {response.status_code}")
                    try:
                        error_data = response.json()
                        print(f"    错误信息: {json.dumps(error_data, ensure_ascii=False, indent=6)}")
                    except:
                        print(f"    响应内容: {response.text[:200]}")
                
            except Exception as e:
                print(f"    ❌ 请求失败: {str(e)}")
        
        return successful_calls
    
    def test_other_kiana_apis_with_anti_content(self):
        """使用anti-content头测试其他Kiana API"""
        print(f"\n🔍 使用anti-content头测试其他Kiana API...")
        
        kiana_endpoints = [
            '/api/kiana/mms/robin/searchForSupplier',
            '/api/kiana/mms/robin/getSupplierList',
            '/api/kiana/mms/robin/getSupplierInfo',
            '/api/kiana/mms/robin/searchForProduct',
            '/api/kiana/mms/robin/getProductList',
            '/api/kiana/mms/robin/getProductInfo',
            '/api/kiana/mms/supplier/list',
            '/api/kiana/mms/supplier/search',
            '/api/kiana/mms/product/list',
            '/api/kiana/mms/product/search'
        ]
        
        additional_successful = []
        
        for endpoint in kiana_endpoints:
            url = f"https://agentseller.temu.com{endpoint}"
            print(f"\n  测试: {endpoint}")
            
            try:
                response = self.session.post(url, json={}, timeout=30)
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'application/json' in content_type:
                        try:
                            result = response.json()
                            print(f"    ✅ 成功!")
                            print(f"    响应: {json.dumps(result, ensure_ascii=False, indent=6)}")
                            
                            additional_successful.append({
                                'endpoint': endpoint,
                                'method': 'POST',
                                'data': {},
                                'response': result,
                                'timestamp': datetime.now().isoformat()
                            })
                            
                        except json.JSONDecodeError:
                            pass
                else:
                    print(f"    ❌ 状态码: {response.status_code}")
                    if response.status_code == 403:
                        try:
                            error = response.json()
                            print(f"    错误: {error}")
                        except:
                            pass
                
            except Exception as e:
                print(f"    ❌ 错误: {str(e)}")
        
        return additional_successful
    
    def save_results(self, all_results):
        """保存测试结果"""
        if not all_results:
            print("\n❌ 没有成功的API调用")
            return None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"anti_content_api_results_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 结果已保存到: {filename}")
        return filename

    def comprehensive_data_extraction(self):
        """使用正确的参数进行全面数据提取"""
        print(f"\n🔍 开始全面数据提取...")

        # 基于成功的参数格式进行数据提取
        successful_params = [
            {'pageNum': 1, 'pageSize': 10},
            {'pageNum': 1, 'pageSize': 20},
            {'pageNum': 1, 'pageSize': 50},
            {'pageNum': 2, 'pageSize': 20},
            {'pageNum': 3, 'pageSize': 20}
        ]

        all_data = []
        url = "https://agentseller.temu.com/api/kiana/mms/robin/searchForSemiSupplier"

        for params in successful_params:
            print(f"\n  提取数据: {params}")

            try:
                response = self.session.post(url, json=params, timeout=30)

                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'application/json' in content_type:
                        try:
                            result = response.json()
                            if result.get('success') and result.get('result'):
                                products = result['result'].get('list', [])
                                print(f"    ✅ 获取到 {len(products)} 个商品")

                                all_data.append({
                                    'params': params,
                                    'response': result,
                                    'timestamp': datetime.now().isoformat(),
                                    'product_count': len(products)
                                })
                            else:
                                print(f"    ⚠️ API返回错误: {result.get('errorMsg', 'Unknown error')}")
                        except json.JSONDecodeError:
                            print(f"    ❌ JSON解析失败")
                else:
                    print(f"    ❌ HTTP错误: {response.status_code}")

            except Exception as e:
                print(f"    ❌ 请求失败: {str(e)}")

        return all_data

def main():
    print("🚀 使用anti-content头进行全面数据提取...")
    print("🔑 这是关键的反爬虫验证头部")

    tester = AntiContentAPITester()

    # 进行全面数据提取
    extracted_data = tester.comprehensive_data_extraction()

    if extracted_data:
        # 保存提取的数据
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"temu_comprehensive_data_{timestamp}.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(extracted_data, f, ensure_ascii=False, indent=2)

        print(f"\n🎉 数据提取完成!")
        print(f"成功提取的数据集数量: {len(extracted_data)}")

        total_products = sum(data.get('product_count', 0) for data in extracted_data)
        print(f"总商品数量: {total_products}")

        print(f"\n📁 数据文件: {filename}")

        # 创建数据摘要
        summary = {
            'extraction_time': datetime.now().isoformat(),
            'total_datasets': len(extracted_data),
            'total_products': total_products,
            'datasets': []
        }

        for data in extracted_data:
            summary['datasets'].append({
                'params': data['params'],
                'product_count': data.get('product_count', 0),
                'timestamp': data['timestamp']
            })

        summary_filename = f"temu_data_summary_{timestamp}.json"
        with open(summary_filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)

        print(f"📊 数据摘要: {summary_filename}")

    else:
        print(f"\n❌ 数据提取失败")
        print("可能需要:")
        print("1. 更新的anti-content值（这个值可能会过期）")
        print("2. 检查参数格式")
        print("3. 验证网络连接")

if __name__ == "__main__":
    main()
