#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import re
import time
import os
from urllib.parse import urljoin, urlparse

class TemuLogin:
    def __init__(self):
        # 彻底禁用代理
        os.environ.pop('HTTP_PROXY', None)
        os.environ.pop('HTTPS_PROXY', None)
        os.environ.pop('http_proxy', None)
        os.environ.pop('https_proxy', None)

        self.session = requests.Session()

        # 禁用代理
        proxies = {'http': None, 'https': None}
        self.session.proxies.update(proxies)
        self.session.trust_env = False
        
        # 设置请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json',
            'Referer': 'https://agentseller.temu.com/',
            'Origin': 'https://agentseller.temu.com'
        }
        self.session.headers.update(headers)
        
    def get_login_page(self):
        """获取登录页面，分析登录流程"""
        print("正在获取登录页面...")

        try:
            # 先尝试seller.temu.com，这个看起来更像是正确的域名
            response = self.session.get('https://seller.temu.com/', timeout=30)
            print(f"seller.temu.com 状态码: {response.status_code}")

            if response.status_code == 200:
                html_content = response.text
                print(f"页面内容长度: {len(html_content)}")

                # 查找可能的登录API端点
                api_patterns = [
                    r'"/api/[^"]*login[^"]*"',
                    r'"/api/[^"]*auth[^"]*"',
                    r'"/api/[^"]*signin[^"]*"',
                    r'"https://[^"]*login[^"]*"',
                    r'"https://[^"]*auth[^"]*"',
                    r'"/bg/[^"]*login[^"]*"',
                    r'"/bg/[^"]*auth[^"]*"'
                ]

                found_apis = []
                for pattern in api_patterns:
                    matches = re.findall(pattern, html_content, re.IGNORECASE)
                    found_apis.extend([match.strip('"') for match in matches])

                print("找到的可能登录API:")
                for api in found_apis[:15]:  # 显示更多
                    print(f"  - {api}")

                # 查找可能的配置信息
                config_patterns = [
                    r'window\.__INITIAL_STATE__\s*=\s*({.*?});',
                    r'window\.config\s*=\s*({.*?});',
                    r'window\.__CONFIG__\s*=\s*({.*?});',
                    r'window\.__PageContext__\s*=\s*({.*?});'
                ]

                for pattern in config_patterns:
                    matches = re.findall(pattern, html_content, re.DOTALL)
                    if matches:
                        print(f"找到配置信息: {matches[0][:300]}...")

                return html_content
            else:
                print(f"获取seller.temu.com失败: {response.status_code}")

                # 备用：尝试agentseller.temu.com
                response = self.session.get('https://agentseller.temu.com/', timeout=30)
                print(f"agentseller.temu.com 状态码: {response.status_code}")
                if response.status_code == 200:
                    return response.text
                return None

        except Exception as e:
            print(f"获取登录页面时出错: {str(e)}")
            return None
    
    def try_common_login_endpoints(self, username, password):
        """尝试常见的登录端点"""
        
        # 常见的登录端点
        login_endpoints = [
            'https://seller.temu.com/api/auth/login',
            'https://seller.temu.com/api/login',
            'https://seller.temu.com/api/user/login',
            'https://seller.temu.com/api/seller/login',
            'https://seller.temu.com/bg/auth/login',
            'https://seller.temu.com/bg/login',
            'https://seller.temu.com/bg/user/login',
            'https://api.temu.com/bg/auth/login',
            'https://api.temu.com/bg/login',
            'https://api.temu.com/seller/login',
            'https://agentseller.temu.com/api/auth/login',
            'https://agentseller.temu.com/api/login'
        ]
        
        # 常见的登录数据格式
        login_data_formats = [
            {
                'username': username,
                'password': password
            },
            {
                'phone': username,
                'password': password
            },
            {
                'mobile': username,
                'password': password
            },
            {
                'account': username,
                'password': password
            }
        ]
        
        for endpoint in login_endpoints:
            print(f"\n尝试登录端点: {endpoint}")
            
            for data_format in login_data_formats:
                try:
                    print(f"  使用数据格式: {list(data_format.keys())}")
                    
                    response = self.session.post(
                        endpoint,
                        json=data_format,
                        timeout=30
                    )
                    
                    print(f"  状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            print(f"  响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                            
                            # 检查是否登录成功
                            if any(key in result for key in ['token', 'access_token', 'success', 'data']):
                                print("  ✅ 可能登录成功!")
                                return True, result
                                
                        except json.JSONDecodeError:
                            print(f"  响应内容: {response.text[:500]}")
                    
                    elif response.status_code == 400:
                        try:
                            error = response.json()
                            print(f"  错误信息: {json.dumps(error, ensure_ascii=False, indent=2)}")
                            
                            # 检查是否需要验证码
                            if any(keyword in str(error).lower() for keyword in ['captcha', 'verify', '验证码', 'code']):
                                print("  ⚠️ 需要验证码!")
                                return False, error
                                
                        except json.JSONDecodeError:
                            print(f"  错误响应: {response.text[:500]}")
                    
                    else:
                        print(f"  其他状态码: {response.status_code}")
                        print(f"  响应: {response.text[:200]}")
                        
                except Exception as e:
                    print(f"  请求失败: {str(e)}")
                    
        return False, None
    
    def login(self, username, password):
        """执行登录"""
        print(f"开始登录流程...")
        print(f"用户名: {username}")
        print(f"密码: {'*' * len(password)}")
        
        # 1. 获取登录页面
        html_content = self.get_login_page()
        if not html_content:
            return False, "无法获取登录页面"
        
        # 2. 尝试常见登录端点
        success, result = self.try_common_login_endpoints(username, password)
        
        if success:
            print("✅ 登录成功!")
            return True, result
        else:
            print("❌ 登录失败")
            return False, result

if __name__ == "__main__":
    # 测试登录
    login_client = TemuLogin()
    
    username = "17319480900"
    password = "Asdty1234"
    
    success, result = login_client.login(username, password)
    
    if success:
        print("\n🎉 登录成功!")
        print("可以开始抓取数据了...")
    else:
        print("\n❌ 登录失败")
        if result:
            print(f"错误信息: {result}")
