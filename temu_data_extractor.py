#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import os
import time
from datetime import datetime

class TemuDataExtractor:
    def __init__(self):
        # 禁用代理
        os.environ.pop('HTTP_PROXY', None)
        os.environ.pop('HTTPS_PROXY', None)
        os.environ.pop('http_proxy', None)
        os.environ.pop('https_proxy', None)
        
        self.session = requests.Session()
        proxies = {'http': None, 'https': None}
        self.session.proxies.update(proxies)
        self.session.trust_env = False
        
        # 设置cookie
        cookie_string = "api_uid=Ct4kjmiNlzU98QBGDn3oAg==; _nano_fp=Xpmyn0golpC8XqT8X9_v~JX55DvIt6xeNJtDofAJ; _bee=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; njrpl=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; dilx=MchfnouEwE7NM0Un4s5iO; hfsc=L3yPfIgw4Dbw15/MeQ==; seller_temp=N_eyJ0IjoiUXlzUVZhMHlIMTcxb1hFemhoUlpMWlA5dkVoUzJoRlFiSjZxRTJOYWVldnFjZWt1aGU2cTV4VWsrY0ZqUWdJU2t5dEI1RVJkUjdXT0xJc21rWmhjVHc9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyNDI3ODU0ODg1MTkwMn0=; mallid=634418224371052"
        
        # 解析cookie字符串
        for cookie in cookie_string.split('; '):
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                self.session.cookies.set(name, value, domain='.temu.com')
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://seller.temu.com/',
            'Origin': 'https://seller.temu.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        })
        
        self.base_url = 'https://seller.temu.com'
        self.successful_endpoints = []
    
    def test_endpoint(self, endpoint, method='GET', data=None):
        """测试单个API端点"""
        try:
            if method == 'GET':
                response = self.session.get(f"{self.base_url}{endpoint}", timeout=30)
            else:
                response = self.session.post(f"{self.base_url}{endpoint}", json=data or {}, timeout=30)
            
            content_type = response.headers.get('content-type', '')
            
            if response.status_code == 200 and 'application/json' in content_type:
                try:
                    data = response.json()
                    return True, data
                except json.JSONDecodeError:
                    return False, None
            
            return False, None
            
        except Exception as e:
            return False, str(e)
    
    def discover_api_endpoints(self):
        """发现有效的API端点"""
        print("🔍 发现API端点...")
        
        # 基于已知模式的端点列表
        endpoints_to_test = [
            # 服务器和系统信息
            '/api/server/_stm',
            '/api/server/time',
            '/api/system/info',
            
            # 账户和用户信息
            '/api/pepino/account/info',
            '/api/pepino/user/info',
            '/api/pepino/profile',
            '/mms/floyd/account/info',
            '/mms/floyd/user/profile',
            
            # 导航和菜单
            '/api/bg/christiaan/pc/navigation',
            '/api/bg/christiaan/menu',
            '/api/bg/navigation/menu',
            
            # 店铺信息
            '/api/bg/store/info',
            '/api/bg/shop/info',
            '/api/bg/merchant/info',
            '/mms/store/basic',
            '/mms/shop/profile',
            
            # 商品相关
            '/api/bg/product/list',
            '/api/bg/goods/list',
            '/api/bg/item/list',
            '/api/bg/spu/list',
            '/api/bg/sku/list',
            '/mms/product/summary',
            '/mms/goods/overview',
            
            # 订单相关
            '/api/bg/order/list',
            '/api/bg/orders/summary',
            '/api/bg/trade/list',
            '/mms/order/overview',
            '/mms/trade/summary',
            
            # 统计和分析
            '/api/bg/analytics/overview',
            '/api/bg/stats/summary',
            '/api/bg/dashboard/data',
            '/mms/analytics/basic',
            '/mms/stats/overview',
            
            # 设置和配置
            '/api/bg/setting/basic',
            '/api/bg/config/user',
            '/mms/setting/profile',
            
            # 消息和通知
            '/api/plateau/conv/needReplyCount',
            '/mms/message/unread',
            '/mms/notification/list'
        ]
        
        for endpoint in endpoints_to_test:
            print(f"测试: {endpoint}")
            
            # 先尝试GET
            success, data = self.test_endpoint(endpoint, 'GET')
            if success:
                print(f"✅ GET {endpoint} - 成功")
                self.successful_endpoints.append({
                    'endpoint': endpoint,
                    'method': 'GET',
                    'data': data
                })
                continue
            
            # 再尝试POST
            success, data = self.test_endpoint(endpoint, 'POST')
            if success:
                print(f"✅ POST {endpoint} - 成功")
                self.successful_endpoints.append({
                    'endpoint': endpoint,
                    'method': 'POST',
                    'data': data
                })
            
            time.sleep(0.1)  # 避免请求过快
    
    def extract_detailed_data(self):
        """提取详细数据"""
        print("\n📊 提取详细数据...")
        
        all_data = {}
        
        for endpoint_info in self.successful_endpoints:
            endpoint = endpoint_info['endpoint']
            method = endpoint_info['method']
            data = endpoint_info['data']
            
            print(f"\n处理端点: {method} {endpoint}")
            
            # 如果是列表类型的端点，尝试获取更多数据
            if any(keyword in endpoint.lower() for keyword in ['list', 'summary', 'overview']):
                # 尝试不同的分页参数
                pagination_params = [
                    {'page': 1, 'size': 50},
                    {'pageNum': 1, 'pageSize': 50},
                    {'offset': 0, 'limit': 50},
                    {'start': 0, 'count': 50}
                ]
                
                for params in pagination_params:
                    success, paginated_data = self.test_endpoint(endpoint, 'POST', params)
                    if success and paginated_data != data:
                        print(f"  ✅ 分页数据获取成功: {params}")
                        data = paginated_data
                        break
            
            all_data[endpoint] = {
                'method': method,
                'data': data,
                'timestamp': datetime.now().isoformat()
            }
        
        return all_data
    
    def save_data(self, data):
        """保存数据到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"temu_data_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 数据已保存到: {filename}")
        
        # 创建一个简化的摘要文件
        summary = {}
        for endpoint, info in data.items():
            summary[endpoint] = {
                'method': info['method'],
                'has_data': bool(info['data']),
                'data_type': type(info['data']).__name__,
                'timestamp': info['timestamp']
            }
            
            # 如果是字典，显示键名
            if isinstance(info['data'], dict):
                summary[endpoint]['keys'] = list(info['data'].keys())
            # 如果是列表，显示长度
            elif isinstance(info['data'], list):
                summary[endpoint]['length'] = len(info['data'])
        
        summary_filename = f"temu_summary_{timestamp}.json"
        with open(summary_filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"📋 摘要已保存到: {summary_filename}")
        
        return filename, summary_filename

def main():
    print("🚀 开始提取Temu卖家中心数据...")
    
    extractor = TemuDataExtractor()
    
    # 发现API端点
    extractor.discover_api_endpoints()
    
    if not extractor.successful_endpoints:
        print("\n❌ 未发现有效的API端点")
        return
    
    print(f"\n✅ 发现 {len(extractor.successful_endpoints)} 个有效端点:")
    for endpoint_info in extractor.successful_endpoints:
        print(f"  - {endpoint_info['method']} {endpoint_info['endpoint']}")
    
    # 提取详细数据
    all_data = extractor.extract_detailed_data()
    
    # 保存数据
    data_file, summary_file = extractor.save_data(all_data)
    
    print(f"\n🎉 数据提取完成!")
    print(f"数据文件: {data_file}")
    print(f"摘要文件: {summary_file}")

if __name__ == "__main__":
    main()
