{"timestamp": "20250802_143819", "analysis_method": "efficient_focused_analysis", "menu_apis": {"total_tested": 1, "successful": 1, "data": {"/api/server/_stm": {"server_time": 1754116689012}}}, "business_apis": {"total_tested": 0, "successful": 0, "data": {}}, "module_analysis": {"total_modules": 8, "fully_accessible": 8, "partially_accessible": 0, "not_accessible": 0, "details": {"商品管理": {"name": "商品管理", "routes": ["/goods", "/products", "/newon/product-select"], "accessible_routes": ["/goods", "/products", "/newon/product-select"], "status": "fully_accessible"}, "订单管理": {"name": "订单管理", "routes": ["/orders", "/sales"], "accessible_routes": ["/orders", "/sales"], "status": "fully_accessible"}, "库存管理": {"name": "库存管理", "routes": ["/stock", "/inventory"], "accessible_routes": ["/stock", "/inventory"], "status": "fully_accessible"}, "数据分析": {"name": "数据分析", "routes": ["/analytics", "/data-center/goods-data"], "accessible_routes": ["/analytics", "/data-center/goods-data"], "status": "fully_accessible"}, "客服管理": {"name": "客服管理", "routes": ["/chat-app", "/customer-consultation"], "accessible_routes": ["/chat-app", "/customer-consultation"], "status": "fully_accessible"}, "财务管理": {"name": "财务管理", "routes": ["/finance", "/payment"], "accessible_routes": ["/finance", "/payment"], "status": "fully_accessible"}, "物流管理": {"name": "物流管理", "routes": ["/lgst", "/shipping"], "accessible_routes": ["/lgst", "/shipping"], "status": "fully_accessible"}, "营销活动": {"name": "营销活动", "routes": ["/activity", "/marketing"], "accessible_routes": ["/activity", "/marketing"], "status": "fully_accessible"}}}, "recommendations": ["✅ 商品管理功能完全可用，建议优先使用商品相关API进行数据提取", "📊 数据分析模块可用，建议定期获取商品和销售数据"]}