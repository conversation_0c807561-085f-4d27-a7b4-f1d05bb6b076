#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
from datetime import datetime
from collections import defaultdict

class TemuDataAnalyzer:
    def __init__(self, data_file):
        self.data_file = data_file
        self.data = None
        self.load_data()
    
    def load_data(self):
        """加载数据文件"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"✅ 成功加载数据文件: {self.data_file}")
            print(f"📊 数据集数量: {len(self.data)}")
        except Exception as e:
            print(f"❌ 加载数据失败: {str(e)}")
            return False
        return True
    
    def analyze_data_structure(self):
        """分析数据结构"""
        print(f"\n🔍 分析数据结构...")
        
        if not self.data:
            print("❌ 没有数据可分析")
            return
        
        total_products = 0
        total_suppliers = set()
        categories = defaultdict(int)
        product_statuses = defaultdict(int)
        
        for dataset in self.data:
            response = dataset.get('response', {})
            result = response.get('result', {})
            
            # 获取商品列表
            products = result.get('dataList', [])
            total_products += len(products)
            
            print(f"\n📦 数据集参数: {dataset.get('params', {})}")
            print(f"   商品数量: {len(products)}")
            print(f"   总计: {result.get('total', 0)}")
            
            # 分析每个商品
            for product in products:
                # 供应商信息
                supplier_id = product.get('supplierId')
                if supplier_id:
                    total_suppliers.add(supplier_id)
                
                # 分类信息
                category_name = product.get('leafCategoryName', 'Unknown')
                categories[category_name] += 1
                
                # 商品状态统计
                skc_list = product.get('skcList', [])
                for skc in skc_list:
                    status = skc.get('selectStatus', 'Unknown')
                    product_statuses[status] += 1
        
        print(f"\n📈 总体统计:")
        print(f"   总商品数: {total_products}")
        print(f"   供应商数: {len(total_suppliers)}")
        print(f"   商品分类数: {len(categories)}")
        
        print(f"\n🏷️ 商品分类分布:")
        for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"   {category}: {count}")
        
        print(f"\n📊 商品状态分布:")
        for status, count in sorted(product_statuses.items(), key=lambda x: x[1], reverse=True):
            print(f"   状态 {status}: {count}")
        
        return {
            'total_products': total_products,
            'total_suppliers': len(total_suppliers),
            'categories': dict(categories),
            'product_statuses': dict(product_statuses)
        }
    
    def extract_product_details(self):
        """提取详细的商品信息"""
        print(f"\n📋 提取商品详细信息...")
        
        all_products = []
        
        for dataset in self.data:
            response = dataset.get('response', {})
            result = response.get('result', {})
            products = result.get('dataList', [])
            
            for product in products:
                product_info = {
                    'product_id': product.get('productId'),
                    'product_name': product.get('productName'),
                    'supplier_id': product.get('supplierId'),
                    'supplier_name': product.get('supplierName'),
                    'category': product.get('leafCategoryName'),
                    'full_category': product.get('fullCategoryName', []),
                    'supplier_price': product.get('supplierPrice'),
                    'currency': product.get('supplierPriceCurrencyType'),
                    'goods_id': product.get('goodsId'),
                    'created_at': product.get('productCreatedAt'),
                    'updated_at': product.get('productUpdatedAt'),
                    'is_semi_hosted': product.get('isSemiHostedProduct'),
                    'site_info': product.get('siteInfoList', []),
                    'carousel_images': product.get('carouselImageUrlList', []),
                    'product_properties': product.get('productPropertyList', []),
                    'skc_count': len(product.get('skcList', [])),
                    'punish_info': product.get('punishInfoList', [])
                }
                
                # 提取SKC信息
                skc_list = product.get('skcList', [])
                product_info['skc_details'] = []
                
                for skc in skc_list:
                    skc_info = {
                        'skc_id': skc.get('skcId'),
                        'goods_skc_id': skc.get('goodsSkcId'),
                        'select_status': skc.get('selectStatus'),
                        'supplier_price': skc.get('supplierPrice'),
                        'ext_code': skc.get('extCode'),
                        'buyer_uid': skc.get('buyerUid'),
                        'sample_needed': skc.get('sampleNeeded'),
                        'preview_images': skc.get('previewImgUrlList', []),
                        'sku_count': len(skc.get('skuList', []))
                    }
                    product_info['skc_details'].append(skc_info)
                
                all_products.append(product_info)
        
        print(f"✅ 提取了 {len(all_products)} 个商品的详细信息")
        return all_products
    
    def save_analysis_results(self, analysis_stats, product_details):
        """保存分析结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存统计分析
        stats_filename = f"temu_analysis_stats_{timestamp}.json"
        with open(stats_filename, 'w', encoding='utf-8') as f:
            json.dump(analysis_stats, f, ensure_ascii=False, indent=2)
        
        # 保存商品详情
        details_filename = f"temu_product_details_{timestamp}.json"
        with open(details_filename, 'w', encoding='utf-8') as f:
            json.dump(product_details, f, ensure_ascii=False, indent=2)
        
        # 创建CSV格式的商品摘要
        csv_filename = f"temu_products_summary_{timestamp}.csv"
        with open(csv_filename, 'w', encoding='utf-8', newline='') as f:
            if product_details:
                # 写入CSV头部
                headers = [
                    'product_id', 'product_name', 'supplier_id', 'supplier_name',
                    'category', 'supplier_price', 'currency', 'goods_id',
                    'is_semi_hosted', 'skc_count', 'created_at'
                ]
                f.write(','.join(headers) + '\n')
                
                # 写入数据行
                for product in product_details:
                    # 安全处理字符串字段
                    product_name = str(product.get('product_name', '')).replace('"', '""')
                    supplier_name = str(product.get('supplier_name', '')).replace('"', '""')
                    category = str(product.get('category', '')).replace('"', '""')

                    row = [
                        str(product.get('product_id', '')),
                        f'"{product_name}"',
                        str(product.get('supplier_id', '')),
                        f'"{supplier_name}"',
                        f'"{category}"',
                        str(product.get('supplier_price', '')),
                        str(product.get('currency', '')),
                        str(product.get('goods_id', '')),
                        str(product.get('is_semi_hosted', '')),
                        str(product.get('skc_count', '')),
                        str(product.get('created_at', ''))
                    ]
                    f.write(','.join(row) + '\n')
        
        print(f"\n💾 分析结果已保存:")
        print(f"   📊 统计数据: {stats_filename}")
        print(f"   📋 商品详情: {details_filename}")
        print(f"   📄 CSV摘要: {csv_filename}")
        
        return stats_filename, details_filename, csv_filename

def main():
    print("🔍 Temu数据分析工具")
    
    # 查找最新的数据文件
    data_files = [f for f in os.listdir('.') if f.startswith('temu_comprehensive_data_') and f.endswith('.json')]
    
    if not data_files:
        print("❌ 未找到数据文件")
        return
    
    # 使用最新的数据文件
    latest_file = sorted(data_files)[-1]
    print(f"📁 使用数据文件: {latest_file}")
    
    # 创建分析器
    analyzer = TemuDataAnalyzer(latest_file)
    
    if not analyzer.data:
        return
    
    # 执行分析
    print(f"\n🚀 开始数据分析...")
    
    # 分析数据结构和统计
    analysis_stats = analyzer.analyze_data_structure()
    
    # 提取商品详情
    product_details = analyzer.extract_product_details()
    
    # 保存结果
    if analysis_stats and product_details:
        analyzer.save_analysis_results(analysis_stats, product_details)
        
        print(f"\n🎉 数据分析完成!")
        print(f"   总商品数: {analysis_stats['total_products']}")
        print(f"   供应商数: {analysis_stats['total_suppliers']}")
        print(f"   分类数: {len(analysis_stats['categories'])}")
    else:
        print(f"\n❌ 数据分析失败")

if __name__ == "__main__":
    main()
