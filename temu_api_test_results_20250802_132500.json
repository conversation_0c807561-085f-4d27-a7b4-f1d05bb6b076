{"https://agentseller.temu.com/api/bg-ladyfish/mms/menu/page/feedback/entrance": {"GET": {"success": false, "error": "Non-JSON response", "content_type": "text/html; charset=utf-8", "content_length": 8959}, "POST": {"success": false, "error": {"success": false, "errorCode": 3000000, "errorMsg": "Bad Request"}, "status_code": 400}}, "https://agentseller.temu.com/quick/merchant/pop/query": {"GET": {"success": false, "error": "Non-JSON response", "content_type": "text/html; charset=utf-8", "content_length": 8959}, "POST": {"success": true, "data": {"success": true, "errorCode": 1000000, "errorMsg": null, "result": {"popIdList": []}}, "status_code": 200}}, "https://us.pftk.temu.com/pmm/api/pmm/api": {"GET": {"success": false, "error": "Non-JSON response", "content_type": "application/octet-stream", "content_length": 0}, "POST": {"success": false, "error": "Non-JSON response", "content_type": "application/octet-stream", "content_length": 0}}}