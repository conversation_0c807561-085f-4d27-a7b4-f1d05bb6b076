#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import os
import re
from datetime import datetime

class TemuGoodsMenuAnalyzer:
    def __init__(self):
        # 禁用代理
        os.environ.pop('HTTP_PROXY', None)
        os.environ.pop('HTTPS_PROXY', None)
        os.environ.pop('http_proxy', None)
        os.environ.pop('https_proxy', None)
        
        self.session = requests.Session()
        proxies = {'http': None, 'https': None}
        self.session.proxies.update(proxies)
        self.session.trust_env = False
        
        # 设置cookie
        cookie_string = "api_uid=Ct4kjmiNlzU98QBGDn3oAg==; _nano_fp=Xpmyn0golpC8XqT8X9_v~JX55DvIt6xeNJtDofAJ; _bee=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; njrpl=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; dilx=MchfnouEwE7NM0Un4s5iO; hfsc=L3yPfIgw4Dbw15/MeQ==; seller_temp=N_eyJ0IjoiUXlzUVZhMHlIMTcxb1hFemhoUlpMWlA5dkVoUzJoRlFiSjZxRTJOYWVldnFjZWt1aGU2cTV4VWsrY0ZqUWdJU2t5dEI1RVJkUjdXT0xJc21rWmhjVHc9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyNDI3ODU0ODg1MTkwMn0=; mallid=634418224371052"
        
        # 解析cookie字符串
        for cookie in cookie_string.split('; '):
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                self.session.cookies.set(name, value, domain='.temu.com')
                self.session.cookies.set(name, value, domain='.agentseller.temu.com')
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Referer': 'https://agentseller.temu.com/',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin'
        })
        
        self.base_url = "https://agentseller.temu.com"
        
        # 商品管理相关的路由
        self.goods_routes = [
            '/goods',
            '/goods/list', 
            '/goods-entry',
            '/products',
            '/product',
            '/items',
            '/customize-goods',
            '/images-edit',
            '/data-center/goods-data',
            '/main/goods-label/make',
            '/main/business-opportunity/goods-detail',
            '/newon/product-select',
            '/visage-agent-seller/product/skc/pageQuery',
            '/open/market/service/goods-rule/main',
            '/open/market/service/goods-rule/order-detail',
            '/open/market/service/goods-rule/order-list'
        ]
    
    def analyze_goods_page(self, route):
        """分析商品管理页面的详细内容"""
        print(f"\n🔍 分析商品页面: {route}")
        
        url = f"{self.base_url}{route}"
        
        try:
            response = self.session.get(url, timeout=30)
            
            if response.status_code == 200:
                html_content = response.text
                
                # 提取页面信息
                page_info = {
                    'route': route,
                    'url': url,
                    'title': self.extract_title(html_content),
                    'menu_items': self.extract_menu_items(html_content),
                    'navigation_items': self.extract_navigation_items(html_content),
                    'button_actions': self.extract_button_actions(html_content),
                    'form_fields': self.extract_form_fields(html_content),
                    'table_headers': self.extract_table_headers(html_content),
                    'api_endpoints': self.extract_api_endpoints(html_content),
                    'component_names': self.extract_component_names(html_content)
                }
                
                print(f"  ✅ 页面标题: {page_info['title']}")
                print(f"  📋 菜单项: {len(page_info['menu_items'])} 个")
                print(f"  🧭 导航项: {len(page_info['navigation_items'])} 个")
                print(f"  🔘 按钮操作: {len(page_info['button_actions'])} 个")
                print(f"  📝 表单字段: {len(page_info['form_fields'])} 个")
                print(f"  📊 表格标题: {len(page_info['table_headers'])} 个")
                print(f"  🔌 API端点: {len(page_info['api_endpoints'])} 个")
                print(f"  🧩 组件: {len(page_info['component_names'])} 个")
                
                return page_info
                
            else:
                print(f"  ❌ 状态码: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"  ❌ 错误: {str(e)}")
            return None
    
    def extract_title(self, html_content):
        """提取页面标题"""
        title_match = re.search(r'<title[^>]*>([^<]+)</title>', html_content, re.IGNORECASE)
        return title_match.group(1).strip() if title_match else None
    
    def extract_menu_items(self, html_content):
        """提取菜单项"""
        menu_patterns = [
            r'menu["\']?[^>]*>([^<]+)',
            r'nav["\']?[^>]*>([^<]+)',
            r'sidebar["\']?[^>]*>([^<]+)',
            r'tab["\']?[^>]*>([^<]+)',
            r'商品[^"\'<>]*["\']?[^>]*>([^<]+)',
            r'产品[^"\'<>]*["\']?[^>]*>([^<]+)',
            r'goods[^"\'<>]*["\']?[^>]*>([^<]+)',
            r'product[^"\'<>]*["\']?[^>]*>([^<]+)'
        ]
        
        menu_items = set()
        for pattern in menu_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if len(match.strip()) > 2 and len(match.strip()) < 50:
                    menu_items.add(match.strip())
        
        return list(menu_items)
    
    def extract_navigation_items(self, html_content):
        """提取导航项"""
        nav_patterns = [
            r'href=["\']([^"\']*goods[^"\']*)["\']',
            r'href=["\']([^"\']*product[^"\']*)["\']',
            r'href=["\']([^"\']*item[^"\']*)["\']',
            r'to=["\']([^"\']*goods[^"\']*)["\']',
            r'to=["\']([^"\']*product[^"\']*)["\']',
            r'路由["\']?[^>]*["\']([^"\']*goods[^"\']*)["\']',
            r'路由["\']?[^>]*["\']([^"\']*product[^"\']*)["\']'
        ]
        
        nav_items = set()
        for pattern in nav_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if match.startswith('/') and len(match) > 1:
                    nav_items.add(match)
        
        return list(nav_items)
    
    def extract_button_actions(self, html_content):
        """提取按钮操作"""
        button_patterns = [
            r'<button[^>]*>([^<]+)</button>',
            r'btn[^>]*>([^<]+)<',
            r'button[^>]*>([^<]+)<',
            r'操作[^>]*>([^<]+)<',
            r'action[^>]*>([^<]+)<'
        ]
        
        buttons = set()
        for pattern in button_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if len(match.strip()) > 1 and len(match.strip()) < 30:
                    buttons.add(match.strip())
        
        return list(buttons)
    
    def extract_form_fields(self, html_content):
        """提取表单字段"""
        field_patterns = [
            r'<input[^>]*name=["\']([^"\']+)["\']',
            r'<select[^>]*name=["\']([^"\']+)["\']',
            r'<textarea[^>]*name=["\']([^"\']+)["\']',
            r'placeholder=["\']([^"\']+)["\']',
            r'label[^>]*>([^<]+)</label>'
        ]
        
        fields = set()
        for pattern in field_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if len(match.strip()) > 1 and len(match.strip()) < 50:
                    fields.add(match.strip())
        
        return list(fields)
    
    def extract_table_headers(self, html_content):
        """提取表格标题"""
        header_patterns = [
            r'<th[^>]*>([^<]+)</th>',
            r'<td[^>]*class[^>]*header[^>]*>([^<]+)</td>',
            r'表头[^>]*>([^<]+)<',
            r'column[^>]*>([^<]+)<'
        ]
        
        headers = set()
        for pattern in header_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if len(match.strip()) > 1 and len(match.strip()) < 30:
                    headers.add(match.strip())
        
        return list(headers)
    
    def extract_api_endpoints(self, html_content):
        """提取API端点"""
        api_patterns = [
            r'["\']([/]api[/][^"\']*goods[^"\']*)["\']',
            r'["\']([/]api[/][^"\']*product[^"\']*)["\']',
            r'["\']([/]api[/][^"\']*item[^"\']*)["\']',
            r'url:\s*["\']([/]api[^"\']*goods[^"\']*)["\']',
            r'url:\s*["\']([/]api[^"\']*product[^"\']*)["\']'
        ]
        
        apis = set()
        for pattern in api_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if match.startswith('/api/'):
                    apis.add(match)
        
        return list(apis)
    
    def extract_component_names(self, html_content):
        """提取组件名称"""
        component_patterns = [
            r'([A-Z][a-zA-Z0-9]*Goods[A-Z][a-zA-Z0-9]*)',
            r'([A-Z][a-zA-Z0-9]*Product[A-Z][a-zA-Z0-9]*)',
            r'([A-Z][a-zA-Z0-9]*Item[A-Z][a-zA-Z0-9]*)',
            r'(Goods[A-Z][a-zA-Z0-9]*)',
            r'(Product[A-Z][a-zA-Z0-9]*)',
            r'(Item[A-Z][a-zA-Z0-9]*)'
        ]
        
        components = set()
        for pattern in component_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                if len(match) > 3:
                    components.add(match)
        
        return list(components)
    
    def test_goods_apis(self):
        """测试商品相关的API"""
        print(f"\n🔍 测试商品相关API...")
        
        # 商品相关的API端点
        goods_apis = [
            '/api/kiana/mms/magneto/price/query-compare-sku-info',
            '/api/bg/vision/mms/product/sku/match/bound/submit',
            '/api/bg/vision/mms/product/sku/relate/batch_switch_shipping_mode',
            '/api/bg/vision/mms/product/sku/relate/query/shipping_mode_product_skc',
            '/api/kiana/firestar/unsold/stock/pageQueryMallWaitHandleTaskUnsoldProduct',
            '/api/kiana/firestar/unsold/stock/queryMallWaitHandleTask',
            '/api/kiana/firestar/unsold/stock/supplierConfirmUnsoldStockHandleMethod',
            '/api/kiana/fenrir/CompetitorGoodsInvitationMmsService/cmpGoodsMmsPage',
            '/api/kiana/fenrir/FullChanceGoodsInvitationMmsService/fullChanceInvitationPop',
            '/api/kiana/gamblers/marketing/enroll/pop/product/list'
        ]
        
        # 设置API请求头
        api_headers = self.session.headers.copy()
        api_headers.update({
            'Accept': '*/*',
            'Content-Type': 'application/json',
            'anti-content': '0aqAfxnYsyPdJgd9Q2I8qlOivTg2VWUwZNpNM3Hc8XcXvoD10n6YmtiazyAOihcFiOvnaiZsdwfnts96iq_CZoPp9t0ZZ2rY0ba41PQgQHTAZUPV-kB6k7MZQNWWKaq8CWlReXtZOKdj0e0vH-qme1kI_FO7_PMZqpyopUPTRZ9maP-YAuDe-9kWD83XJa4xyvFzglM1FkCdXtbmCytLPQthBX4hE0aTJpwNwOASpmux89n67EHo_KLqFBBN3uKLZWNC4tL9POo6rYGGj9A6EuKzgB6b2kX5nGdGqPBui9hK9_qL0O_V0WSk8pIVZQoWEx-3HCFl6Ys3xdscPCRfTnos7y4qH2Oi0g32E8TiEY8SIHjvIild8cmyTc_UGwOtrl7bR_gEOCewuldnBHIACNDnJ7ldQKXCrQ_e-uQJtr01VtJ1AJ7jfunRwJGqKEkTw9NCOmONefDNhFsp3yTGJSsoAJ6L8XAq9uZiiLjpHC3nuunH-krjki1-HxfGXRblF_5YRqGXfgSP89HJN2CocuOEGPRDxYpH4anLv4lOkUjfJI1zERQmDzywnHQrBgYnkdoqdSNeuExozpky2vlNgCHhJLEoJt7MsD8OcwuXdhQB7q4ytxCslcl_568YubpbeCSVMKDDvizZEpOUnUv4f8ir3A08088_3uvC3nrr4pzyGVH1LEac5nSAyQ6upzZiw8a_bWjnbmX5aFPn0DA1fL1BskDuH8Qd7N96orM9k7dPXH8',
            'mallid': '634418224371052'
        })
        
        working_apis = []
        
        for api in goods_apis:
            url = f"{self.base_url}{api}"
            print(f"  测试API: {api}")
            
            # 尝试GET和POST
            for method in ['GET', 'POST']:
                try:
                    if method == 'GET':
                        response = self.session.get(url, headers=api_headers, timeout=10)
                    else:
                        response = self.session.post(url, headers=api_headers, json={}, timeout=10)
                    
                    if response.status_code == 200:
                        content_type = response.headers.get('content-type', '')
                        if 'application/json' in content_type:
                            try:
                                data = response.json()
                                print(f"    ✅ {method}成功 (JSON)")
                                working_apis.append({
                                    'endpoint': api,
                                    'method': method,
                                    'status_code': response.status_code,
                                    'response_type': 'JSON',
                                    'sample_response': str(data)[:200] + '...' if len(str(data)) > 200 else str(data)
                                })
                                break
                            except:
                                print(f"    ✅ {method}成功 (非JSON)")
                        break
                    elif response.status_code == 405 and method == 'GET':
                        continue
                    else:
                        if method == 'POST':
                            print(f"    ❌ 失败 ({response.status_code})")
                        
                except Exception as e:
                    if method == 'POST':
                        print(f"    ❌ 错误: {str(e)}")
        
        return working_apis
    
    def save_analysis_results(self, page_analyses, working_apis):
        """保存分析结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        results = {
            'timestamp': timestamp,
            'analysis_type': 'goods_management_menu',
            'page_analyses': page_analyses,
            'working_apis': working_apis,
            'summary': {
                'total_pages_analyzed': len(page_analyses),
                'total_working_apis': len(working_apis),
                'total_menu_items': sum(len(p.get('menu_items', [])) for p in page_analyses if p),
                'total_navigation_items': sum(len(p.get('navigation_items', [])) for p in page_analyses if p),
                'total_button_actions': sum(len(p.get('button_actions', [])) for p in page_analyses if p)
            }
        }
        
        filename = f"temu_goods_menu_analysis_{timestamp}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 商品管理菜单分析结果已保存: {filename}")
        return filename

def main():
    print("🚀 Temu商品管理菜单分析工具")
    print("🔍 深入分析商品管理相关页面的菜单结构")
    
    analyzer = TemuGoodsMenuAnalyzer()
    
    # 1. 分析商品管理相关页面
    page_analyses = []
    for route in analyzer.goods_routes:
        page_info = analyzer.analyze_goods_page(route)
        if page_info:
            page_analyses.append(page_info)
    
    # 2. 测试商品相关API
    working_apis = analyzer.test_goods_apis()
    
    # 3. 保存结果
    filename = analyzer.save_analysis_results(page_analyses, working_apis)
    
    print(f"\n🎉 商品管理菜单分析完成!")
    print(f"   分析页面: {len(page_analyses)}")
    print(f"   工作API: {len(working_apis)}")
    print(f"   结果文件: {filename}")

if __name__ == "__main__":
    main()
