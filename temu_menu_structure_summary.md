# Temu卖家后台菜单结构和路由总结

## 概览
通过系统性探索Temu卖家后台(https://agentseller.temu.com)，我们发现了完整的菜单结构和功能模块。

## 主要发现统计
- **总发现路由**: 375个
- **可访问路由**: 278个  
- **发现API端点**: 79个
- **工作中的API**: 10个
- **菜单项**: 1703个
- **前端组件**: 63个

## 主要功能模块

### 1. 主控制台 (/main)
- `/main` - 主页面
- `/main/dashboard` - 仪表板
- `/main/authentication` - 认证管理
- `/main/business-opportunity` - 商机分析
- `/main/business-opportunity/goods-detail` - 商品详情
- `/main/business-opportunity/market-detail` - 市场详情
- `/main/category-analysis` - 类目分析
- `/main/course/detail` - 课程详情
- `/main/course/list` - 课程列表
- `/main/evaluate-manage` - 评价管理
- `/main/evaluate/evaluate-list` - 评价列表
- `/main/examination/detail` - 考试详情
- `/main/examination/process` - 考试流程
- `/main/flow-grow` - 流量增长
- `/main/flux-analysis` - 流量分析
- `/main/goods-label/make` - 商品标签制作
- `/main/market-analysis` - 市场分析
- `/main/survey` - 调研
- `/main/survey/edit` - 调研编辑

### 2. 商品管理 (/goods, /products)
- `/goods` - 商品管理
- `/goods/list` - 商品列表
- `/products` - 产品管理
- `/product` - 产品页面
- `/items` - 商品项目
- `/customize-goods` - 定制商品
- `/images-edit` - 图片编辑

### 3. 新品选品 (/newon)
- `/newon` - 新品选品主页
- `/newon/product-select` - 产品选择
- `/newon/sourcing` - 采购

### 4. 半托管 (/semi)
- `/semi` - 半托管主页
- `/semi/products` - 半托管商品
- `/semi/orders` - 半托管订单
- `/semi/analytics` - 半托管分析

### 5. 库存管理 (/stock)
- `/stock` - 库存管理
- `/stock/inventory` - 库存
- `/stock/management` - 库存管理
- `/stock/fully-mgt/order-manage` - 全托管订单管理
- `/stock/fully-mgt/order-manage-custom` - 定制订单管理
- `/stock/fully-mgt/order-manage-urgency` - 紧急订单管理
- `/stock/fully-mgt/production-suggestion` - 生产建议
- `/stock-edit` - 库存编辑

### 6. 物流管理 (/lgst)
- `/lgst` - 物流管理
- `/lgst/shipping` - 发货管理
- `/lgst/tracking` - 物流跟踪
- `/lgst/warehouse` - 仓库管理

### 7. 订单管理 (/orders)
- `/orders` - 订单管理
- `/order` - 订单页面
- `/sales` - 销售
- `/transactions` - 交易

### 8. 客服聊天 (/chat-app)
- `/chat-app` - 客服应用
- `/chat-app/contact-buyer` - 联系买家
- `/chat-app/index` - 聊天首页
- `/chat-app/service-indicator` - 服务指标
- `/chat-app/ticket-detail` - 工单详情
- `/chat-app/ticket-list` - 工单列表
- `/chat-app/upload-template` - 上传模板
- `/chat-app/messages` - 消息
- `/chat-app/support` - 支持

### 9. 劳务管理 (/labor)
- `/labor` - 劳务管理
- `/labor/management` - 劳务管理
- `/labor/analytics` - 劳务分析
- `/labor/credit-card-paying` - 信用卡支付
- `/labor/tax-document-library` - 税务文档库
- `/labor/tax-settle` - 税务结算

### 10. 数据分析
- `/analytics` - 分析
- `/reports` - 报告
- `/statistics` - 统计
- `/data` - 数据
- `/data-center/goods-data` - 商品数据中心
- `/data-analysis` - 数据分析
- `/business-intelligence` - 商业智能

### 11. 营销活动 (/activity)
- `/activity` - 活动管理
- `/activity/marketing-activity` - 营销活动
- `/activity/marketing-activity/auto-sign` - 自动报名
- `/activity/marketing-activity/detail-new` - 活动详情
- `/activity/marketing-activity/detail-new-result` - 活动结果
- `/activity/marketing-activity/detail/worry-free` - 无忧活动
- `/activity/marketing-activity/hot-girl-cloth` - 热门女装
- `/activity/marketing-activity/log` - 活动日志
- `/activity/marketing-activity/mdeast-subsidy` - 中东补贴
- `/activity/marketing-activity/result` - 活动结果
- `/activity/marketing-activity/semi-detail` - 半托管详情
- `/activity/marketing-activity/spring-activity` - 春季活动
- `/activity/marketing-tools/create-coupon` - 创建优惠券
- `/activity/marketing-tools/create-mall-coupon` - 创建店铺优惠券
- `/activity/marketing-tools/create-recommend-coupon` - 创建推荐优惠券

### 12. 财务管理 (/finance)
- `/finance` - 财务管理
- `/payment` - 支付
- `/billing` - 账单
- `/revenue` - 收入

### 13. 设置管理
- `/settings` - 设置
- `/config` - 配置
- `/profile` - 个人资料
- `/account` - 账户
- `/mall/account-info` - 店铺账户信息
- `/mall/entity-info` - 实体信息

### 14. 开放平台 (/open-platform)
- `/open-platform` - 开放平台
- `/open-platform/entry` - 开放平台入口
- `/open-platform/system-manage/authorize` - 系统管理授权
- `/open/mall-decoration/editor` - 店铺装修编辑器
- `/open/market/service/all` - 市场服务
- `/open/market/service/goods-rule/main` - 商品规则
- `/open/market/service/graph/main` - 图表服务
- `/open/market/service/logistics/main` - 物流服务
- `/open/market/service/material/main` - 素材服务
- `/open/market/service/rule/main` - 规则服务
- `/open/market/service/video/order-create` - 视频订单创建
- `/open/market/service/video/order-list` - 视频订单列表

### 15. 补货管理 (/restock)
- `/restock/order-manage` - 补货订单管理
- `/restock/plan` - 补货计划
- `/restock/stocking-shipping-receipt-details` - 备货发货收据详情
- `/restock/stocking-shipping-receipt/calculate` - 备货发货计算
- `/restock/stocking-shipping-receipt/operate` - 备货发货操作

### 16. 合规管理
- `/check-compliance` - 合规检查
- `/copyright-guide` - 版权指南
- `/entity-edit-compliance` - 实体编辑合规
- `/entity-replace-compliance` - 实体替换合规
- `/mall-transfer-compliance` - 店铺转移合规

### 17. 治理平台 (/govern-platform)
- `/govern-platform` - 治理平台
- `/govern-platform/entry` - 治理平台入口
- `/govern-platform/mobile-device-code/agreement` - 移动设备代码协议

### 18. 样品管理 (/sample)
- `/sample` - 样品管理

### 19. 素材管理 (/material)
- `/material` - 素材管理
- `/material/guide/edit` - 素材指南编辑

### 20. 仓储管理 (/wms)
- `/wms` - 仓储管理系统

## 重要API端点

### 认证相关
- `/api/seller/auth/loginByCode` - 验证码登录
- `/api/seller/auth/logout` - 登出
- `/api/seller/auth/menu` - 菜单权限
- `/api/seller/auth/userInfo` - 用户信息

### 数据查询
- `/api/server/_stm` - 服务器状态
- `/api/charge_back/query/status/count` - 退款状态统计

### 文件上传
- `/api/galerie/cos_large_file/upload_init` - 大文件上传初始化
- `/api/galerie/cos_large_file/upload_part` - 大文件分片上传
- `/api/galerie/cos_large_file/upload_complete` - 大文件上传完成
- `/api/galerie/file/signature` - 文件签名
- `/api/galerie/image/signature` - 图片签名

### 价格管理
- `/api/kiana/mms/magneto/price/query-compare-sku-info` - 价格对比查询
- `/api/kiana/magnus/mms/price/reviewDetail` - 价格审核详情

### 仓储物流
- `/api/bg/cw/getCwProviderList` - 获取仓储服务商列表
- `/api/bg/cw/mallCwTokenAuthorization` - 仓储授权

## 技术架构特点

1. **单页应用(SPA)**: 所有路由都返回相同的HTML页面，由前端JavaScript处理路由
2. **微前端架构**: 不同功能模块可能由不同的前端应用组成
3. **权限控制**: 大部分API需要特定的权限和认证头
4. **模块化设计**: 功能按业务领域清晰分离

## 下一步建议

1. **深入特定模块**: 选择重点关注的业务模块进行深入分析
2. **API参数研究**: 分析工作中的API的具体参数要求
3. **权限映射**: 研究不同用户角色对应的菜单权限
4. **数据流分析**: 追踪关键业务流程的数据传递路径

---
*生成时间: 2025-08-02 14:19*
*数据来源: Temu卖家后台系统性探索*
