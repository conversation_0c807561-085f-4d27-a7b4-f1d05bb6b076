#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import os
import time
from datetime import datetime

class TemuAPIFinder:
    def __init__(self):
        # 禁用代理
        os.environ.pop('HTTP_PROXY', None)
        os.environ.pop('HTTPS_PROXY', None)
        os.environ.pop('http_proxy', None)
        os.environ.pop('https_proxy', None)
        
        self.session = requests.Session()
        proxies = {'http': None, 'https': None}
        self.session.proxies.update(proxies)
        self.session.trust_env = False
        
        # 设置cookie
        cookie_string = "api_uid=Ct4kjmiNlzU98QBGDn3oAg==; _nano_fp=Xpmyn0golpC8XqT8X9_v~JX55DvIt6xeNJtDofAJ; _bee=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; njrpl=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; dilx=MchfnouEwE7NM0Un4s5iO; hfsc=L3yPfIgw4Dbw15/MeQ==; seller_temp=N_eyJ0IjoiUXlzUVZhMHlIMTcxb1hFemhoUlpMWlA5dkVoUzJoRlFiSjZxRTJOYWVldnFjZWt1aGU2cTV4VWsrY0ZqUWdJU2t5dEI1RVJkUjdXT0xJc21rWmhjVHc9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyNDI3ODU0ODg1MTkwMn0=; mallid=634418224371052"
        
        # 解析cookie字符串
        for cookie in cookie_string.split('; '):
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                self.session.cookies.set(name, value, domain='.temu.com')
                self.session.cookies.set(name, value, domain='.agentseller.temu.com')
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://agentseller.temu.com/',
            'Origin': 'https://agentseller.temu.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Content-Type': 'application/json'
        })
        
        self.successful_apis = []
    
    def test_api_variations(self, base_endpoint):
        """测试API端点的不同变体"""
        variations = [
            base_endpoint,
            base_endpoint + '/query',
            base_endpoint + '/list',
            base_endpoint + '/info',
            base_endpoint + '/get',
            base_endpoint + '/data',
            base_endpoint + '/summary'
        ]
        
        for endpoint in variations:
            url = f"https://agentseller.temu.com{endpoint}"
            
            try:
                response = self.session.post(url, json={}, timeout=30)
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'application/json' in content_type:
                        try:
                            result = response.json()
                            print(f"✅ 发现有效API: {endpoint}")
                            print(f"   响应: {json.dumps(result, ensure_ascii=False, indent=4)}")
                            
                            self.successful_apis.append({
                                'endpoint': endpoint,
                                'data': result,
                                'timestamp': datetime.now().isoformat()
                            })
                            
                        except json.JSONDecodeError:
                            pass
                
            except Exception as e:
                pass
            
            time.sleep(0.1)
    
    def explore_merchant_apis(self):
        """探索商户相关API"""
        print("🏪 探索商户相关API...")
        
        merchant_bases = [
            '/quick/merchant',
            '/quick/seller',
            '/quick/vendor',
            '/api/merchant',
            '/api/seller',
            '/api/vendor'
        ]
        
        for base in merchant_bases:
            print(f"  测试基础路径: {base}")
            self.test_api_variations(base)
    
    def explore_store_apis(self):
        """探索店铺相关API"""
        print("\n🏬 探索店铺相关API...")
        
        store_bases = [
            '/quick/store',
            '/quick/shop',
            '/api/store',
            '/api/shop'
        ]
        
        for base in store_bases:
            print(f"  测试基础路径: {base}")
            self.test_api_variations(base)
    
    def explore_product_apis(self):
        """探索商品相关API"""
        print("\n📦 探索商品相关API...")
        
        product_bases = [
            '/quick/product',
            '/quick/goods',
            '/quick/item',
            '/api/product',
            '/api/goods',
            '/api/item'
        ]
        
        for base in product_bases:
            print(f"  测试基础路径: {base}")
            self.test_api_variations(base)
    
    def explore_order_apis(self):
        """探索订单相关API"""
        print("\n📋 探索订单相关API...")
        
        order_bases = [
            '/quick/order',
            '/quick/orders',
            '/quick/trade',
            '/api/order',
            '/api/orders',
            '/api/trade'
        ]
        
        for base in order_bases:
            print(f"  测试基础路径: {base}")
            self.test_api_variations(base)
    
    def explore_analytics_apis(self):
        """探索分析统计API"""
        print("\n📊 探索分析统计API...")
        
        analytics_bases = [
            '/quick/analytics',
            '/quick/stats',
            '/quick/dashboard',
            '/quick/report',
            '/api/analytics',
            '/api/stats',
            '/api/dashboard',
            '/api/report'
        ]
        
        for base in analytics_bases:
            print(f"  测试基础路径: {base}")
            self.test_api_variations(base)
    
    def try_specific_patterns(self):
        """尝试特定的API模式"""
        print("\n🎯 尝试特定的API模式...")
        
        # 基于已知成功的模式
        specific_endpoints = [
            '/quick/merchant/pop/query',  # 已知有效
            '/quick/merchant/notification/query',
            '/quick/merchant/message/query',
            '/quick/merchant/alert/query',
            '/quick/merchant/status/query',
            '/quick/store/pop/query',
            '/quick/product/pop/query',
            '/quick/order/pop/query',
            '/quick/notification/pop/query',
            '/quick/message/pop/query',
            '/quick/alert/pop/query',
            '/quick/popup/query',
            '/quick/banner/query',
            '/quick/notice/query'
        ]
        
        for endpoint in specific_endpoints:
            url = f"https://agentseller.temu.com{endpoint}"
            
            try:
                print(f"  测试: {endpoint}")
                response = self.session.post(url, json={}, timeout=30)
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'application/json' in content_type:
                        try:
                            result = response.json()
                            print(f"    ✅ 成功!")
                            print(f"    响应: {json.dumps(result, ensure_ascii=False, indent=6)}")
                            
                            self.successful_apis.append({
                                'endpoint': endpoint,
                                'data': result,
                                'timestamp': datetime.now().isoformat()
                            })
                            
                        except json.JSONDecodeError:
                            pass
                else:
                    print(f"    ❌ 状态码: {response.status_code}")
                
            except Exception as e:
                print(f"    ❌ 错误: {str(e)}")
            
            time.sleep(0.1)
    
    def try_with_different_params(self):
        """使用不同参数尝试已知端点"""
        print("\n🔧 使用不同参数尝试API...")
        
        base_endpoint = '/quick/merchant/pop/query'
        
        param_sets = [
            {},
            {'type': 'all'},
            {'status': 'active'},
            {'category': 'notification'},
            {'limit': 10},
            {'page': 1},
            {'merchantId': '634418224371052'},  # 从cookie中的mallid
            {'storeId': '634418224371052'},
            {'popType': 'notification'},
            {'popType': 'message'},
            {'popType': 'alert'},
            {'popType': 'banner'}
        ]
        
        for params in param_sets:
            try:
                url = f"https://agentseller.temu.com{base_endpoint}"
                response = self.session.post(url, json=params, timeout=30)
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'application/json' in content_type:
                        try:
                            result = response.json()
                            if result != {"success": True, "errorCode": 1000000, "errorMsg": None, "result": {"popIdList": []}}:
                                print(f"  ✅ 不同响应! 参数: {params}")
                                print(f"    响应: {json.dumps(result, ensure_ascii=False, indent=4)}")
                                
                                self.successful_apis.append({
                                    'endpoint': base_endpoint,
                                    'params': params,
                                    'data': result,
                                    'timestamp': datetime.now().isoformat()
                                })
                        except json.JSONDecodeError:
                            pass
                
            except Exception as e:
                pass
            
            time.sleep(0.1)
    
    def save_results(self):
        """保存结果"""
        if not self.successful_apis:
            print("\n❌ 没有发现新的有效API")
            return None, None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"temu_discovered_apis_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.successful_apis, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 结果已保存到: {filename}")
        
        # 创建摘要
        summary = {
            'total_apis': len(self.successful_apis),
            'unique_endpoints': list(set(api['endpoint'] for api in self.successful_apis)),
            'timestamp': datetime.now().isoformat()
        }
        
        summary_filename = f"temu_api_summary_{timestamp}.json"
        with open(summary_filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"📋 摘要已保存到: {summary_filename}")
        
        return filename, summary_filename

def main():
    print("🔍 深度搜索Temu API端点...")
    
    finder = TemuAPIFinder()
    
    # 探索不同类型的API
    finder.explore_merchant_apis()
    finder.explore_store_apis()
    finder.explore_product_apis()
    finder.explore_order_apis()
    finder.explore_analytics_apis()
    finder.try_specific_patterns()
    finder.try_with_different_params()
    
    # 保存结果
    if finder.successful_apis:
        data_file, summary_file = finder.save_results()
        
        print(f"\n🎉 搜索完成!")
        print(f"发现 {len(finder.successful_apis)} 个有效的API调用:")
        
        for api in finder.successful_apis:
            endpoint = api['endpoint']
            params = api.get('params', '')
            if params:
                print(f"  ✅ {endpoint} (参数: {params})")
            else:
                print(f"  ✅ {endpoint}")
        
        if data_file:
            print(f"\n📁 文件:")
            print(f"  数据: {data_file}")
            print(f"  摘要: {summary_file}")
    else:
        print("\n❌ 未发现新的有效API端点")
        print("建议:")
        print("1. 检查cookie是否仍然有效")
        print("2. 在浏览器中查看网络请求，获取更多真实的API端点")
        print("3. 可能需要特定的请求头或参数")

if __name__ == "__main__":
    main()
