#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import re
import os
from urllib.parse import urljoin, urlparse

def analyze_temu_page():
    """深入分析Temu页面，寻找真正的API端点"""
    
    # 禁用代理
    os.environ.pop('HTTP_PROXY', None)
    os.environ.pop('HTTPS_PROXY', None)
    os.environ.pop('http_proxy', None)
    os.environ.pop('https_proxy', None)
    
    session = requests.Session()
    proxies = {'http': None, 'https': None}
    session.proxies.update(proxies)
    session.trust_env = False
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    session.headers.update(headers)
    
    print("正在分析seller.temu.com页面...")
    
    try:
        response = session.get('https://seller.temu.com/', timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            html_content = response.text
            print(f"页面内容长度: {len(html_content)}")
            
            # 保存完整页面内容到文件
            with open('seller_page.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            print("页面内容已保存到 seller_page.html")
            
            # 查找所有script标签中的内容
            script_pattern = r'<script[^>]*>(.*?)</script>'
            scripts = re.findall(script_pattern, html_content, re.DOTALL)
            print(f"找到 {len(scripts)} 个script标签")
            
            # 分析每个script标签
            for i, script in enumerate(scripts):
                if len(script.strip()) > 100:  # 只分析有内容的script
                    print(f"\n=== Script {i+1} ===")
                    print(f"长度: {len(script)}")
                    
                    # 查找API相关的内容
                    api_patterns = [
                        r'["\']https?://[^"\']*api[^"\']*["\']',
                        r'["\']https?://[^"\']*login[^"\']*["\']',
                        r'["\']https?://[^"\']*auth[^"\']*["\']',
                        r'["\']https?://[^"\']*bg[^"\']*["\']',
                        r'["\']\/api\/[^"\']*["\']',
                        r'["\']\/bg\/[^"\']*["\']'
                    ]
                    
                    found_apis = []
                    for pattern in api_patterns:
                        matches = re.findall(pattern, script, re.IGNORECASE)
                        found_apis.extend([match.strip('"\'') for match in matches])
                    
                    if found_apis:
                        print("找到的API端点:")
                        for api in set(found_apis):  # 去重
                            print(f"  - {api}")
                    
                    # 查找配置对象
                    config_patterns = [
                        r'window\.__[A-Z_]+__\s*=\s*({.*?});',
                        r'window\.[a-zA-Z_]+\s*=\s*({.*?});',
                        r'const\s+[a-zA-Z_]+\s*=\s*({.*?});',
                        r'var\s+[a-zA-Z_]+\s*=\s*({.*?});'
                    ]
                    
                    for pattern in config_patterns:
                        matches = re.findall(pattern, script, re.DOTALL)
                        if matches:
                            for match in matches[:3]:  # 只显示前3个
                                try:
                                    # 尝试解析JSON
                                    config = json.loads(match)
                                    print(f"找到配置对象: {json.dumps(config, ensure_ascii=False, indent=2)[:500]}...")
                                except:
                                    print(f"找到配置对象 (无法解析): {match[:200]}...")
            
            # 查找可能的登录表单
            form_pattern = r'<form[^>]*>(.*?)</form>'
            forms = re.findall(form_pattern, html_content, re.DOTALL | re.IGNORECASE)
            if forms:
                print(f"\n找到 {len(forms)} 个表单:")
                for i, form in enumerate(forms):
                    print(f"表单 {i+1}: {form[:200]}...")
            
            # 查找input字段
            input_pattern = r'<input[^>]*>'
            inputs = re.findall(input_pattern, html_content, re.IGNORECASE)
            if inputs:
                print(f"\n找到 {len(inputs)} 个input字段:")
                for inp in inputs[:10]:  # 只显示前10个
                    print(f"  {inp}")
            
            return html_content
            
    except Exception as e:
        print(f"分析页面时出错: {str(e)}")
        return None

if __name__ == "__main__":
    analyze_temu_page()
