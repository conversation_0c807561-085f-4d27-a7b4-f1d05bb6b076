#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import os
import re
from datetime import datetime
from urllib.parse import urljoin

class TemuDeepExplorer:
    def __init__(self):
        # 禁用代理
        os.environ.pop('HTTP_PROXY', None)
        os.environ.pop('HTTPS_PROXY', None)
        os.environ.pop('http_proxy', None)
        os.environ.pop('https_proxy', None)
        
        self.session = requests.Session()
        proxies = {'http': None, 'https': None}
        self.session.proxies.update(proxies)
        self.session.trust_env = False
        
        # 设置cookie
        cookie_string = "api_uid=Ct4kjmiNlzU98QBGDn3oAg==; _nano_fp=Xpmyn0golpC8XqT8X9_v~JX55DvIt6xeNJtDofAJ; _bee=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; njrpl=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; dilx=MchfnouEwE7NM0Un4s5iO; hfsc=L3yPfIgw4Dbw15/MeQ==; seller_temp=N_eyJ0IjoiUXlzUVZhMHlIMTcxb1hFemhoUlpMWlA5dkVoUzJoRlFiSjZxRTJOYWVldnFjZWt1aGU2cTV4VWsrY0ZqUWdJU2t5dEI1RVJkUjdXT0xJc21rWmhjVHc9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyNDI3ODU0ODg1MTkwMn0=; mallid=634418224371052"
        
        # 解析cookie字符串
        for cookie in cookie_string.split('; '):
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                self.session.cookies.set(name, value, domain='.temu.com')
                self.session.cookies.set(name, value, domain='.agentseller.temu.com')
        
        # 设置完整的请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Cache-Control': 'max-age=0'
        })
        
        self.base_url = "https://agentseller.temu.com"
        
        # 常见的卖家后台路由模式
        self.common_routes = [
            # 主要功能模块
            '/dashboard', '/home', '/overview', '/summary',
            '/products', '/product', '/goods', '/items',
            '/orders', '/order', '/sales', '/transactions',
            '/inventory', '/stock', '/warehouse',
            '/analytics', '/reports', '/statistics', '/data',
            '/settings', '/config', '/profile', '/account',
            '/finance', '/payment', '/billing', '/revenue',
            '/shipping', '/logistics', '/delivery',
            '/customers', '/buyers', '/users',
            '/marketing', '/promotion', '/ads', '/campaign',
            '/support', '/help', '/service', '/chat',
            
            # Temu特定路径
            '/newon', '/semi', '/supplier', '/sourcing',
            '/product-select', '/product-management',
            '/order-management', '/inventory-management',
            '/data-analysis', '/business-intelligence',
            '/seller-center', '/merchant', '/vendor',
            
            # 子路径组合
            '/main/dashboard', '/main/products', '/main/orders',
            '/main/analytics', '/main/settings', '/main/finance',
            '/newon/product-select', '/newon/sourcing',
            '/semi/products', '/semi/orders', '/semi/analytics',
            '/lgst/shipping', '/lgst/tracking', '/lgst/warehouse',
            '/labor/management', '/labor/analytics',
            '/stock/inventory', '/stock/management',
            '/chat-app/messages', '/chat-app/support'
        ]
    
    def explore_known_routes(self):
        """探索已知的路由"""
        print(f"🔍 探索已知路由...")
        
        known_routes = ['/chat-app', '/labor', '/lgst', '/main', '/stock']
        route_details = {}
        
        for route in known_routes:
            print(f"\n📄 探索路由: {route}")
            url = urljoin(self.base_url, route)
            
            try:
                response = self.session.get(url, timeout=30)
                
                if response.status_code == 200:
                    html_content = response.text
                    
                    # 提取页面信息
                    route_info = {
                        'url': url,
                        'status_code': response.status_code,
                        'content_length': len(html_content),
                        'title': self.extract_title(html_content),
                        'scripts': self.extract_scripts(html_content),
                        'api_calls': self.extract_api_calls(html_content),
                        'menu_items': self.extract_menu_items(html_content),
                        'routes_in_js': self.extract_routes_from_js(html_content)
                    }
                    
                    route_details[route] = route_info
                    
                    print(f"  ✅ 页面标题: {route_info['title']}")
                    print(f"  📜 脚本文件: {len(route_info['scripts'])} 个")
                    print(f"  🔌 API调用: {len(route_info['api_calls'])} 个")
                    print(f"  🧭 菜单项: {len(route_info['menu_items'])} 个")
                    print(f"  🛣️ JS路由: {len(route_info['routes_in_js'])} 个")
                    
                else:
                    print(f"  ❌ 状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ 错误: {str(e)}")
        
        return route_details
    
    def extract_title(self, html_content):
        """提取页面标题"""
        title_match = re.search(r'<title[^>]*>([^<]+)</title>', html_content, re.IGNORECASE)
        return title_match.group(1).strip() if title_match else None
    
    def extract_scripts(self, html_content):
        """提取脚本文件"""
        script_pattern = r'<script[^>]*src=["\']([^"\']+)["\']'
        scripts = re.findall(script_pattern, html_content)
        return [script for script in scripts if not script.startswith('data:')]
    
    def extract_api_calls(self, html_content):
        """提取API调用"""
        api_patterns = [
            r'["\']([/]api[/][^"\']+)["\']',
            r'url:\s*["\']([/]api[^"\']+)["\']',
            r'endpoint:\s*["\']([/]api[^"\']+)["\']',
            r'fetch\(["\']([/]api[^"\']+)["\']',
            r'axios\.[a-z]+\(["\']([/]api[^"\']+)["\']'
        ]
        
        api_calls = set()
        for pattern in api_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                if match.startswith('/api/'):
                    api_calls.add(match)
        
        return list(api_calls)
    
    def extract_menu_items(self, html_content):
        """提取菜单项"""
        # 查找可能的菜单结构
        menu_patterns = [
            r'menu["\']?[^>]*>([^<]+)',
            r'nav["\']?[^>]*>([^<]+)',
            r'sidebar["\']?[^>]*>([^<]+)',
            r'["\']([^"\']*(?:dashboard|product|order|analytic|setting|finance)[^"\']*)["\']'
        ]
        
        menu_items = set()
        for pattern in menu_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if len(match.strip()) > 2 and len(match.strip()) < 50:
                    menu_items.add(match.strip())
        
        return list(menu_items)
    
    def extract_routes_from_js(self, html_content):
        """从JavaScript中提取路由"""
        route_patterns = [
            r'path:\s*["\']([^"\']+)["\']',
            r'route:\s*["\']([^"\']+)["\']',
            r'to:\s*["\']([^"\']+)["\']',
            r'href:\s*["\']([^"\']+)["\']',
            r'["\']([/][a-zA-Z0-9\-_/]+)["\']'
        ]
        
        routes = set()
        for pattern in route_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                if (match.startswith('/') and 
                    len(match) > 1 and 
                    not match.startswith('//') and
                    not any(ext in match for ext in ['.js', '.css', '.png', '.jpg', '.ico', '.svg'])):
                    routes.add(match)
        
        return list(routes)
    
    def test_common_routes(self):
        """测试常见路由"""
        print(f"\n🔍 测试常见卖家后台路由...")
        
        accessible_routes = []
        
        for route in self.common_routes:
            url = urljoin(self.base_url, route)
            print(f"  测试: {route}")
            
            try:
                response = self.session.get(url, timeout=10)
                
                if response.status_code == 200:
                    print(f"    ✅ 可访问")
                    accessible_routes.append({
                        'route': route,
                        'url': url,
                        'status_code': response.status_code,
                        'title': self.extract_title(response.text)
                    })
                elif response.status_code in [301, 302]:
                    location = response.headers.get('location', '')
                    print(f"    🔄 重定向到: {location}")
                    accessible_routes.append({
                        'route': route,
                        'url': url,
                        'status_code': response.status_code,
                        'redirect_to': location
                    })
                elif response.status_code == 404:
                    print(f"    ❌ 未找到")
                elif response.status_code == 403:
                    print(f"    🚫 权限不足")
                else:
                    print(f"    ⚠️ 状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"    ❌ 错误: {str(e)}")
        
        return accessible_routes
    
    def discover_api_endpoints(self, route_details):
        """从路由详情中发现API端点"""
        print(f"\n🔍 从页面内容中发现API端点...")
        
        all_apis = set()
        
        for route, details in route_details.items():
            api_calls = details.get('api_calls', [])
            for api in api_calls:
                all_apis.add(api)
        
        # 测试发现的API端点
        working_apis = []
        
        if all_apis:
            print(f"  发现 {len(all_apis)} 个API端点，开始测试...")
            
            # 设置API请求头
            api_headers = self.session.headers.copy()
            api_headers.update({
                'Accept': '*/*',
                'Content-Type': 'application/json',
                'Referer': 'https://agentseller.temu.com/',
                'anti-content': '0aqAfxnYsyPdJgd9Q2I8qlOivTg2VWUwZNpNM3Hc8XcXvoD10n6YmtiazyAOihcFiOvnaiZsdwfnts96iq_CZoPp9t0ZZ2rY0ba41PQgQHTAZUPV-kB6k7MZQNWWKaq8CWlReXtZOKdj0e0vH-qme1kI_FO7_PMZqpyopUPTRZ9maP-YAuDe-9kWD83XJa4xyvFzglM1FkCdXtbmCytLPQthBX4hE0aTJpwNwOASpmux89n67EHo_KLqFBBN3uKLZWNC4tL9POo6rYGGj9A6EuKzgB6b2kX5nGdGqPBui9hK9_qL0O_V0WSk8pIVZQoWEx-3HCFl6Ys3xdscPCRfTnos7y4qH2Oi0g32E8TiEY8SIHjvIild8cmyTc_UGwOtrl7bR_gEOCewuldnBHIACNDnJ7ldQKXCrQ_e-uQJtr01VtJ1AJ7jfunRwJGqKEkTw9NCOmONefDNhFsp3yTGJSsoAJ6L8XAq9uZiiLjpHC3nuunH-krjki1-HxfGXRblF_5YRqGXfgSP89HJN2CocuOEGPRDxYpH4anLv4lOkUjfJI1zERQmDzywnHQrBgYnkdoqdSNeuExozpky2vlNgCHhJLEoJt7MsD8OcwuXdhQB7q4ytxCslcl_568YubpbeCSVMKDDvizZEpOUnUv4f8ir3A08088_3uvC3nrr4pzyGVH1LEac5nSAyQ6upzZiw8a_bWjnbmX5aFPn0DA1fL1BskDuH8Qd7N96orM9k7dPXH8',
                'mallid': '634418224371052'
            })
            
            for api in sorted(all_apis):
                url = urljoin(self.base_url, api)
                print(f"    测试API: {api}")
                
                # 尝试GET和POST
                for method in ['GET', 'POST']:
                    try:
                        if method == 'GET':
                            response = self.session.get(url, headers=api_headers, timeout=10)
                        else:
                            response = self.session.post(url, headers=api_headers, json={}, timeout=10)
                        
                        if response.status_code == 200:
                            content_type = response.headers.get('content-type', '')
                            if 'application/json' in content_type:
                                try:
                                    data = response.json()
                                    print(f"      ✅ {method}成功 (JSON)")
                                    working_apis.append({
                                        'endpoint': api,
                                        'method': method,
                                        'status_code': response.status_code,
                                        'response_type': 'JSON',
                                        'sample_response': str(data)[:200] + '...' if len(str(data)) > 200 else str(data)
                                    })
                                    break  # 成功了就不用测试其他方法
                                except:
                                    print(f"      ✅ {method}成功 (非JSON)")
                            break  # 成功了就不用测试其他方法
                        elif response.status_code == 405 and method == 'GET':
                            continue  # 尝试POST
                        else:
                            if method == 'POST':  # 只在POST失败时报告
                                print(f"      ❌ 失败 ({response.status_code})")
                            
                    except Exception as e:
                        if method == 'POST':  # 只在POST失败时报告
                            print(f"      ❌ 错误: {str(e)}")
        
        return working_apis
    
    def save_results(self, route_details, accessible_routes, working_apis):
        """保存探索结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存详细路由信息
        details_filename = f"temu_route_details_{timestamp}.json"
        with open(details_filename, 'w', encoding='utf-8') as f:
            json.dump(route_details, f, ensure_ascii=False, indent=2)
        
        # 保存可访问路由
        routes_filename = f"temu_accessible_routes_{timestamp}.json"
        with open(routes_filename, 'w', encoding='utf-8') as f:
            json.dump(accessible_routes, f, ensure_ascii=False, indent=2)
        
        # 保存工作中的API
        apis_filename = f"temu_working_apis_{timestamp}.json"
        with open(apis_filename, 'w', encoding='utf-8') as f:
            json.dump(working_apis, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 深度探索结果已保存:")
        print(f"   📋 路由详情: {details_filename}")
        print(f"   🛣️ 可访问路由: {routes_filename}")
        print(f"   🔌 工作API: {apis_filename}")
        
        return details_filename, routes_filename, apis_filename

def main():
    print("🚀 Temu卖家后台深度探索工具")
    print("🔍 深入分析页面结构、菜单和API")
    
    explorer = TemuDeepExplorer()
    
    # 1. 探索已知路由的详细信息
    route_details = explorer.explore_known_routes()
    
    # 2. 测试常见路由
    accessible_routes = explorer.test_common_routes()
    
    # 3. 发现API端点
    working_apis = explorer.discover_api_endpoints(route_details)
    
    # 4. 保存结果
    explorer.save_results(route_details, accessible_routes, working_apis)
    
    print(f"\n🎉 深度探索完成!")
    print(f"   已知路由详情: {len(route_details)}")
    print(f"   新发现可访问路由: {len(accessible_routes)}")
    print(f"   工作中的API: {len(working_apis)}")

if __name__ == "__main__":
    main()
