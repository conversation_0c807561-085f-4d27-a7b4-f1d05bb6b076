#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import os
import re
from datetime import datetime
from urllib.parse import urljoin, urlparse

class TemuRouteExplorer:
    def __init__(self):
        # 禁用代理
        os.environ.pop('HTTP_PROXY', None)
        os.environ.pop('HTTPS_PROXY', None)
        os.environ.pop('http_proxy', None)
        os.environ.pop('https_proxy', None)
        
        self.session = requests.Session()
        proxies = {'http': None, 'https': None}
        self.session.proxies.update(proxies)
        self.session.trust_env = False
        
        # 设置cookie
        cookie_string = "api_uid=Ct4kjmiNlzU98QBGDn3oAg==; _nano_fp=Xpmyn0golpC8XqT8X9_v~JX55DvIt6xeNJtDofAJ; _bee=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; njrpl=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; dilx=MchfnouEwE7NM0Un4s5iO; hfsc=L3yPfIgw4Dbw15/MeQ==; seller_temp=N_eyJ0IjoiUXlzUVZhMHlIMTcxb1hFemhoUlpMWlA5dkVoUzJoRlFiSjZxRTJOYWVldnFjZWt1aGU2cTV4VWsrY0ZqUWdJU2t5dEI1RVJkUjdXT0xJc21rWmhjVHc9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyNDI3ODU0ODg1MTkwMn0=; mallid=634418224371052"
        
        # 解析cookie字符串
        for cookie in cookie_string.split('; '):
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                self.session.cookies.set(name, value, domain='.temu.com')
                self.session.cookies.set(name, value, domain='.agentseller.temu.com')
        
        # 设置完整的请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Cache-Control': 'max-age=0'
        })
        
        self.base_url = "https://agentseller.temu.com"
        self.discovered_routes = set()
        self.api_endpoints = set()
    
    def fetch_main_page(self):
        """获取主页面内容"""
        print(f"🔍 获取主页面: {self.base_url}")
        
        try:
            response = self.session.get(self.base_url, timeout=30)
            
            if response.status_code == 200:
                print(f"✅ 主页面获取成功 (状态码: {response.status_code})")
                return response.text
            else:
                print(f"❌ 主页面获取失败 (状态码: {response.status_code})")
                return None
                
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
            return None
    
    def extract_routes_from_html(self, html_content):
        """从HTML内容中提取路由信息"""
        print(f"🔍 从HTML中提取路由...")
        
        routes = set()
        
        # 提取href链接
        href_pattern = r'href=["\']([^"\']+)["\']'
        href_matches = re.findall(href_pattern, html_content)
        
        for href in href_matches:
            if href.startswith('/'):
                routes.add(href)
            elif href.startswith('https://agentseller.temu.com'):
                path = urlparse(href).path
                if path:
                    routes.add(path)
        
        # 提取JavaScript中的路由定义
        js_route_patterns = [
            r'path:\s*["\']([^"\']+)["\']',
            r'route:\s*["\']([^"\']+)["\']',
            r'to:\s*["\']([^"\']+)["\']',
            r'["\']([/][a-zA-Z0-9\-_/]+)["\']'
        ]
        
        for pattern in js_route_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                if match.startswith('/') and len(match) > 1:
                    routes.add(match)
        
        # 过滤掉明显不是路由的链接
        filtered_routes = set()
        for route in routes:
            if (not route.startswith('//') and 
                not route.startswith('http') and
                not route.endswith('.js') and
                not route.endswith('.css') and
                not route.endswith('.png') and
                not route.endswith('.jpg') and
                not route.endswith('.ico') and
                len(route) > 1):
                filtered_routes.add(route)
        
        print(f"✅ 提取到 {len(filtered_routes)} 个潜在路由")
        return filtered_routes
    
    def extract_api_endpoints_from_html(self, html_content):
        """从HTML中提取API端点"""
        print(f"🔍 从HTML中提取API端点...")
        
        api_endpoints = set()
        
        # 提取API路径
        api_patterns = [
            r'["\']([/]api[/][^"\']+)["\']',
            r'url:\s*["\']([/]api[^"\']+)["\']',
            r'endpoint:\s*["\']([/]api[^"\']+)["\']',
            r'["\']([/]api[/][a-zA-Z0-9\-_/]+)["\']'
        ]
        
        for pattern in api_patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                if match.startswith('/api/'):
                    api_endpoints.add(match)
        
        print(f"✅ 提取到 {len(api_endpoints)} 个API端点")
        return api_endpoints
    
    def test_routes(self, routes):
        """测试路由是否可访问"""
        print(f"🔍 测试 {len(routes)} 个路由...")
        
        accessible_routes = []
        
        for route in sorted(routes):
            url = urljoin(self.base_url, route)
            print(f"  测试: {route}")
            
            try:
                response = self.session.get(url, timeout=10)
                
                if response.status_code == 200:
                    print(f"    ✅ 可访问 (200)")
                    accessible_routes.append({
                        'route': route,
                        'url': url,
                        'status_code': response.status_code,
                        'content_type': response.headers.get('content-type', ''),
                        'title': self.extract_page_title(response.text)
                    })
                elif response.status_code == 302 or response.status_code == 301:
                    location = response.headers.get('location', '')
                    print(f"    🔄 重定向 ({response.status_code}) -> {location}")
                    accessible_routes.append({
                        'route': route,
                        'url': url,
                        'status_code': response.status_code,
                        'redirect_to': location
                    })
                elif response.status_code == 403:
                    print(f"    🚫 权限不足 (403)")
                elif response.status_code == 404:
                    print(f"    ❌ 未找到 (404)")
                else:
                    print(f"    ⚠️ 状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"    ❌ 错误: {str(e)}")
        
        return accessible_routes
    
    def extract_page_title(self, html_content):
        """提取页面标题"""
        title_match = re.search(r'<title[^>]*>([^<]+)</title>', html_content, re.IGNORECASE)
        if title_match:
            return title_match.group(1).strip()
        return None
    
    def test_api_endpoints(self, endpoints):
        """测试API端点"""
        print(f"🔍 测试 {len(endpoints)} 个API端点...")
        
        working_apis = []
        
        # 设置API请求头
        api_headers = self.session.headers.copy()
        api_headers.update({
            'Accept': '*/*',
            'Content-Type': 'application/json',
            'Referer': 'https://agentseller.temu.com/',
            'anti-content': '0aqAfxnYsyPdJgd9Q2I8qlOivTg2VWUwZNpNM3Hc8XcXvoD10n6YmtiazyAOihcFiOvnaiZsdwfnts96iq_CZoPp9t0ZZ2rY0ba41PQgQHTAZUPV-kB6k7MZQNWWKaq8CWlReXtZOKdj0e0vH-qme1kI_FO7_PMZqpyopUPTRZ9maP-YAuDe-9kWD83XJa4xyvFzglM1FkCdXtbmCytLPQthBX4hE0aTJpwNwOASpmux89n67EHo_KLqFBBN3uKLZWNC4tL9POo6rYGGj9A6EuKzgB6b2kX5nGdGqPBui9hK9_qL0O_V0WSk8pIVZQoWEx-3HCFl6Ys3xdscPCRfTnos7y4qH2Oi0g32E8TiEY8SIHjvIild8cmyTc_UGwOtrl7bR_gEOCewuldnBHIACNDnJ7ldQKXCrQ_e-uQJtr01VtJ1AJ7jfunRwJGqKEkTw9NCOmONefDNhFsp3yTGJSsoAJ6L8XAq9uZiiLjpHC3nuunH-krjki1-HxfGXRblF_5YRqGXfgSP89HJN2CocuOEGPRDxYpH4anLv4lOkUjfJI1zERQmDzywnHQrBgYnkdoqdSNeuExozpky2vlNgCHhJLEoJt7MsD8OcwuXdhQB7q4ytxCslcl_568YubpbeCSVMKDDvizZEpOUnUv4f8ir3A08088_3uvC3nrr4pzyGVH1LEac5nSAyQ6upzZiw8a_bWjnbmX5aFPn0DA1fL1BskDuH8Qd7N96orM9k7dPXH8',
            'mallid': '634418224371052'
        })
        
        for endpoint in sorted(endpoints):
            url = urljoin(self.base_url, endpoint)
            print(f"  测试API: {endpoint}")
            
            # 测试GET请求
            try:
                response = self.session.get(url, headers=api_headers, timeout=10)
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'application/json' in content_type:
                        try:
                            data = response.json()
                            print(f"    ✅ GET成功 (JSON)")
                            working_apis.append({
                                'endpoint': endpoint,
                                'method': 'GET',
                                'status_code': response.status_code,
                                'response_type': 'JSON',
                                'sample_response': str(data)[:200] + '...' if len(str(data)) > 200 else str(data)
                            })
                        except:
                            print(f"    ✅ GET成功 (非JSON)")
                    else:
                        print(f"    ✅ GET成功 (HTML/其他)")
                elif response.status_code == 405:
                    print(f"    ⚠️ GET不支持 (405) - 尝试POST")
                    # 尝试POST请求
                    try:
                        post_response = self.session.post(url, headers=api_headers, json={}, timeout=10)
                        if post_response.status_code == 200:
                            content_type = post_response.headers.get('content-type', '')
                            if 'application/json' in content_type:
                                try:
                                    data = post_response.json()
                                    print(f"    ✅ POST成功 (JSON)")
                                    working_apis.append({
                                        'endpoint': endpoint,
                                        'method': 'POST',
                                        'status_code': post_response.status_code,
                                        'response_type': 'JSON',
                                        'sample_response': str(data)[:200] + '...' if len(str(data)) > 200 else str(data)
                                    })
                                except:
                                    print(f"    ✅ POST成功 (非JSON)")
                        else:
                            print(f"    ❌ POST失败 ({post_response.status_code})")
                    except:
                        print(f"    ❌ POST请求失败")
                else:
                    print(f"    ❌ 状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"    ❌ 错误: {str(e)}")
        
        return working_apis
    
    def save_results(self, routes, apis, html_content):
        """保存探索结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存路由信息
        routes_filename = f"temu_routes_{timestamp}.json"
        with open(routes_filename, 'w', encoding='utf-8') as f:
            json.dump(routes, f, ensure_ascii=False, indent=2)
        
        # 保存API信息
        apis_filename = f"temu_apis_{timestamp}.json"
        with open(apis_filename, 'w', encoding='utf-8') as f:
            json.dump(apis, f, ensure_ascii=False, indent=2)
        
        # 保存HTML内容
        html_filename = f"temu_main_page_{timestamp}.html"
        with open(html_filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"\n💾 探索结果已保存:")
        print(f"   🛣️ 路由信息: {routes_filename}")
        print(f"   🔌 API信息: {apis_filename}")
        print(f"   📄 HTML页面: {html_filename}")
        
        return routes_filename, apis_filename, html_filename

def main():
    print("🚀 Temu卖家后台路由探索工具")
    print("🔍 系统性地发现页面结构和API端点")
    
    explorer = TemuRouteExplorer()
    
    # 1. 获取主页面
    html_content = explorer.fetch_main_page()
    if not html_content:
        print("❌ 无法获取主页面，退出")
        return
    
    # 2. 提取路由和API端点
    routes = explorer.extract_routes_from_html(html_content)
    api_endpoints = explorer.extract_api_endpoints_from_html(html_content)
    
    print(f"\n📊 发现概况:")
    print(f"   路由数量: {len(routes)}")
    print(f"   API端点数量: {len(api_endpoints)}")
    
    # 3. 测试路由
    accessible_routes = explorer.test_routes(routes)
    
    # 4. 测试API端点
    working_apis = explorer.test_api_endpoints(api_endpoints)
    
    # 5. 保存结果
    explorer.save_results(accessible_routes, working_apis, html_content)
    
    print(f"\n🎉 探索完成!")
    print(f"   可访问路由: {len(accessible_routes)}")
    print(f"   工作中的API: {len(working_apis)}")

if __name__ == "__main__":
    main()
