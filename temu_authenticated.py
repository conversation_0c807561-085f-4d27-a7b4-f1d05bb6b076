#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import os
from urllib.parse import urljoin

class TemuAuthenticatedClient:
    def __init__(self):
        # 禁用代理
        os.environ.pop('HTTP_PROXY', None)
        os.environ.pop('HTTPS_PROXY', None)
        os.environ.pop('http_proxy', None)
        os.environ.pop('https_proxy', None)
        
        self.session = requests.Session()
        proxies = {'http': None, 'https': None}
        self.session.proxies.update(proxies)
        self.session.trust_env = False
        
        # 设置cookie
        cookie_string = "api_uid=Ct4kjmiNlzU98QBGDn3oAg==; _nano_fp=Xpmyn0golpC8XqT8X9_v~JX55DvIt6xeNJtDofAJ; _bee=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; njrpl=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; dilx=MchfnouEwE7NM0Un4s5iO; hfsc=L3yPfIgw4Dbw15/MeQ==; seller_temp=N_eyJ0IjoiUXlzUVZhMHlIMTcxb1hFemhoUlpMWlA5dkVoUzJoRlFiSjZxRTJOYWVldnFjZWt1aGU2cTV4VWsrY0ZqUWdJU2t5dEI1RVJkUjdXT0xJc21rWmhjVHc9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyNDI3ODU0ODg1MTkwMn0=; mallid=634418224371052"
        
        # 解析cookie字符串
        for cookie in cookie_string.split('; '):
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                self.session.cookies.set(name, value, domain='.temu.com')
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://seller.temu.com/',
            'Origin': 'https://seller.temu.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Content-Type': 'application/json'
        })
        
        self.base_url = 'https://seller.temu.com'
    
    def test_authentication(self):
        """测试认证是否有效"""
        print("🔐 测试认证状态...")
        
        # 尝试访问账户信息API
        test_endpoints = [
            '/api/pepino/account/info/get',
            '/api/bg/pawpaw/auth/check',
            '/api/bg/sigerus/auth/status',
            '/mms/floyd/account/base_info',
            '/api/bg/christiaan/pc/navigation/info'
        ]
        
        for endpoint in test_endpoints:
            try:
                print(f"\n尝试访问: {endpoint}")
                response = self.session.post(self.base_url + endpoint, json={}, timeout=30)
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"✅ 成功响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                        return True, endpoint, data
                    except json.JSONDecodeError:
                        print(f"响应内容: {response.text[:500]}")
                
                elif response.status_code == 401:
                    print("🔒 认证失败")
                elif response.status_code == 403:
                    print("🚫 权限不足")
                else:
                    print(f"其他状态码: {response.status_code}")
                    try:
                        error = response.json()
                        print(f"错误信息: {json.dumps(error, ensure_ascii=False, indent=2)}")
                    except:
                        print(f"响应内容: {response.text[:200]}")
                        
            except Exception as e:
                print(f"请求失败: {str(e)}")
        
        return False, None, None
    
    def get_seller_info(self):
        """获取卖家信息"""
        print("\n📊 获取卖家信息...")
        
        endpoints = [
            '/api/pepino/account/info/get',
            '/mms/floyd/account/base_info',
            '/api/bg/christiaan/pc/navigation/info'
        ]
        
        for endpoint in endpoints:
            try:
                response = self.session.post(self.base_url + endpoint, json={}, timeout=30)
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 卖家信息 ({endpoint}):")
                    print(json.dumps(data, ensure_ascii=False, indent=2))
                    return data
            except Exception as e:
                print(f"获取失败 ({endpoint}): {str(e)}")
        
        return None
    
    def get_store_data(self):
        """获取店铺数据"""
        print("\n🏪 获取店铺数据...")
        
        # 可能的店铺数据API
        store_endpoints = [
            '/api/bg/store/info',
            '/api/bg/store/basic',
            '/api/bg/store/profile',
            '/mms/store/info',
            '/bg/store/dashboard'
        ]
        
        for endpoint in store_endpoints:
            try:
                response = self.session.post(self.base_url + endpoint, json={}, timeout=30)
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 店铺数据 ({endpoint}):")
                    print(json.dumps(data, ensure_ascii=False, indent=2))
                    return data
            except Exception as e:
                print(f"获取失败 ({endpoint}): {str(e)}")
        
        return None
    
    def get_product_data(self):
        """获取商品数据"""
        print("\n📦 获取商品数据...")
        
        # 可能的商品数据API
        product_endpoints = [
            '/api/bg/product/list',
            '/api/bg/goods/list',
            '/api/bg/item/list',
            '/mms/product/list',
            '/bg/product/dashboard'
        ]
        
        for endpoint in product_endpoints:
            try:
                # 尝试不同的请求参数
                params_list = [
                    {},
                    {'page': 1, 'size': 10},
                    {'pageNum': 1, 'pageSize': 10},
                    {'offset': 0, 'limit': 10}
                ]
                
                for params in params_list:
                    response = self.session.post(self.base_url + endpoint, json=params, timeout=30)
                    if response.status_code == 200:
                        data = response.json()
                        print(f"✅ 商品数据 ({endpoint}):")
                        print(json.dumps(data, ensure_ascii=False, indent=2))
                        return data
                        
            except Exception as e:
                print(f"获取失败 ({endpoint}): {str(e)}")
        
        return None
    
    def get_order_data(self):
        """获取订单数据"""
        print("\n📋 获取订单数据...")
        
        # 可能的订单数据API
        order_endpoints = [
            '/api/bg/order/list',
            '/api/bg/orders/list',
            '/mms/order/list',
            '/bg/order/dashboard'
        ]
        
        for endpoint in order_endpoints:
            try:
                # 尝试不同的请求参数
                params_list = [
                    {},
                    {'page': 1, 'size': 10},
                    {'pageNum': 1, 'pageSize': 10},
                    {'offset': 0, 'limit': 10}
                ]
                
                for params in params_list:
                    response = self.session.post(self.base_url + endpoint, json=params, timeout=30)
                    if response.status_code == 200:
                        data = response.json()
                        print(f"✅ 订单数据 ({endpoint}):")
                        print(json.dumps(data, ensure_ascii=False, indent=2))
                        return data
                        
            except Exception as e:
                print(f"获取失败 ({endpoint}): {str(e)}")
        
        return None

def main():
    print("🚀 开始使用Cookie认证访问Temu卖家中心...")
    
    client = TemuAuthenticatedClient()
    
    # 测试认证
    auth_success, endpoint, data = client.test_authentication()
    
    if auth_success:
        print(f"\n🎉 认证成功! 有效端点: {endpoint}")
        
        # 获取各种数据
        seller_info = client.get_seller_info()
        store_data = client.get_store_data()
        product_data = client.get_product_data()
        order_data = client.get_order_data()
        
        print("\n📊 数据获取完成!")
        
    else:
        print("\n❌ 认证失败，可能需要重新获取Cookie")
        print("请确保:")
        print("1. Cookie是最新的")
        print("2. 浏览器中仍然保持登录状态")
        print("3. 没有触发额外的安全验证")

if __name__ == "__main__":
    main()
