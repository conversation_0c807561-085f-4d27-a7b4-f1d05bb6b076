#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import re
import os

def try_login_apis():
    """尝试找到的登录API端点"""
    
    # 禁用代理
    os.environ.pop('HTTP_PROXY', None)
    os.environ.pop('HTTPS_PROXY', None)
    os.environ.pop('http_proxy', None)
    os.environ.pop('https_proxy', None)
    
    session = requests.Session()
    proxies = {'http': None, 'https': None}
    session.proxies.update(proxies)
    session.trust_env = False
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Referer': 'https://seller.temu.com/',
        'Origin': 'https://seller.temu.com'
    }
    session.headers.update(headers)
    
    # 基于找到的API模式，尝试可能的登录端点
    base_urls = [
        'https://seller.temu.com',
        'https://api.temu.com'
    ]
    
    login_endpoints = [
        '/api/bg/pawpaw/auth/login',
        '/api/bg/sigerus/auth/login',
        '/api/bg/auth/login',
        '/api/bg/login',
        '/api/pepino/auth/login',
        '/api/pepino/login',
        '/bg/auth/login',
        '/bg/login'
    ]
    
    username = "***********"
    password = "Asdty1234"
    
    # 不同的数据格式
    login_data_formats = [
        {
            'username': username,
            'password': password
        },
        {
            'phone': username,
            'password': password
        },
        {
            'mobile': username,
            'password': password
        },
        {
            'account': username,
            'password': password
        },
        {
            'loginAccount': username,
            'password': password
        },
        {
            'phoneNumber': username,
            'password': password
        }
    ]
    
    print("正在尝试基于JavaScript分析的API端点...")
    
    for base_url in base_urls:
        for endpoint in login_endpoints:
            full_url = base_url + endpoint
            print(f"\n尝试登录端点: {full_url}")
            
            for i, data_format in enumerate(login_data_formats):
                try:
                    print(f"  数据格式 {i+1}: {list(data_format.keys())}")
                    
                    response = session.post(
                        full_url,
                        json=data_format,
                        timeout=30
                    )
                    
                    print(f"  状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            print(f"  ✅ 成功响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                            
                            # 检查是否登录成功
                            success_indicators = ['token', 'access_token', 'success', 'data', 'user', 'account']
                            if any(key in result for key in success_indicators):
                                print("  🎉 可能登录成功!")
                                return True, full_url, result
                                
                        except json.JSONDecodeError:
                            print(f"  响应内容: {response.text[:500]}")
                    
                    elif response.status_code == 400:
                        try:
                            error = response.json()
                            print(f"  ⚠️ 错误响应: {json.dumps(error, ensure_ascii=False, indent=2)}")
                            
                            # 检查是否需要验证码
                            captcha_indicators = ['captcha', 'verify', '验证码', 'code', 'challenge']
                            error_str = str(error).lower()
                            if any(keyword in error_str for keyword in captcha_indicators):
                                print("  🔐 需要验证码!")
                                return False, full_url, error
                                
                        except json.JSONDecodeError:
                            print(f"  错误响应: {response.text[:500]}")
                    
                    elif response.status_code == 401:
                        print(f"  🔒 认证失败")
                        try:
                            error = response.json()
                            print(f"  错误详情: {json.dumps(error, ensure_ascii=False, indent=2)}")
                        except:
                            print(f"  错误内容: {response.text[:200]}")
                    
                    elif response.status_code == 403:
                        print(f"  🚫 禁止访问")
                        try:
                            error = response.json()
                            print(f"  错误详情: {json.dumps(error, ensure_ascii=False, indent=2)}")
                        except:
                            print(f"  错误内容: {response.text[:200]}")
                    
                    elif response.status_code == 404:
                        print(f"  ❌ 端点不存在")
                        break  # 这个端点不存在，尝试下一个
                    
                    elif response.status_code == 405:
                        print(f"  ❌ 方法不允许")
                        break  # 这个端点不支持POST，尝试下一个
                    
                    else:
                        print(f"  其他状态码: {response.status_code}")
                        print(f"  响应: {response.text[:200]}")
                        
                except Exception as e:
                    print(f"  请求失败: {str(e)}")
    
    print("\n❌ 未找到有效的登录API")
    return False, None, None

if __name__ == "__main__":
    success, url, result = try_login_apis()
    
    if success:
        print(f"\n🎉 登录成功!")
        print(f"API端点: {url}")
        print("可以开始抓取数据了...")
    else:
        print(f"\n❌ 登录失败")
        if url and result:
            print(f"最有希望的端点: {url}")
            print(f"错误信息: {result}")
            print("\n可能需要验证码或其他认证步骤")
