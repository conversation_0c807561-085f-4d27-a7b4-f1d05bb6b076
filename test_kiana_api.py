#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import os
from datetime import datetime

class KianaAPITester:
    def __init__(self):
        # 禁用代理
        os.environ.pop('HTTP_PROXY', None)
        os.environ.pop('HTTPS_PROXY', None)
        os.environ.pop('http_proxy', None)
        os.environ.pop('https_proxy', None)
        
        self.session = requests.Session()
        proxies = {'http': None, 'https': None}
        self.session.proxies.update(proxies)
        self.session.trust_env = False
        
        # 设置cookie
        cookie_string = "api_uid=Ct4kjmiNlzU98QBGDn3oAg==; _nano_fp=Xpmyn0golpC8XqT8X9_v~JX55DvIt6xeNJtDofAJ; _bee=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; njrpl=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; dilx=MchfnouEwE7NM0Un4s5iO; hfsc=L3yPfIgw4Dbw15/MeQ==; seller_temp=N_eyJ0IjoiUXlzUVZhMHlIMTcxb1hFemhoUlpMWlA5dkVoUzJoRlFiSjZxRTJOYWVldnFjZWt1aGU2cTV4VWsrY0ZqUWdJU2t5dEI1RVJkUjdXT0xJc21rWmhjVHc9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyNDI3ODU0ODg1MTkwMn0=; mallid=634418224371052"
        
        # 解析cookie字符串
        for cookie in cookie_string.split('; '):
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                self.session.cookies.set(name, value, domain='.temu.com')
                self.session.cookies.set(name, value, domain='.agentseller.temu.com')
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://agentseller.temu.com/',
            'Origin': 'https://agentseller.temu.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Content-Type': 'application/json'
        })
    
    def test_kiana_api(self):
        """测试Kiana API端点"""
        url = "https://agentseller.temu.com/api/kiana/mms/robin/searchForSemiSupplier"
        
        print(f"🔍 测试API: {url}")
        
        # 尝试不同的请求方法和参数
        test_cases = [
            # GET请求
            {'method': 'GET', 'data': None},
            
            # POST请求 - 空参数
            {'method': 'POST', 'data': {}},
            
            # POST请求 - 搜索参数
            {'method': 'POST', 'data': {'keyword': ''}},
            {'method': 'POST', 'data': {'search': ''}},
            {'method': 'POST', 'data': {'query': ''}},
            
            # POST请求 - 分页参数
            {'method': 'POST', 'data': {'page': 1, 'size': 10}},
            {'method': 'POST', 'data': {'pageNum': 1, 'pageSize': 10}},
            {'method': 'POST', 'data': {'offset': 0, 'limit': 10}},
            
            # POST请求 - 供应商相关参数
            {'method': 'POST', 'data': {'supplierType': 'semi'}},
            {'method': 'POST', 'data': {'type': 'semi'}},
            {'method': 'POST', 'data': {'category': 'all'}},
            {'method': 'POST', 'data': {'status': 'active'}},
            
            # POST请求 - 组合参数
            {'method': 'POST', 'data': {
                'keyword': '',
                'page': 1,
                'size': 10,
                'type': 'semi'
            }},
            {'method': 'POST', 'data': {
                'search': '',
                'pageNum': 1,
                'pageSize': 10,
                'supplierType': 'semi'
            }}
        ]
        
        successful_calls = []
        
        for i, test_case in enumerate(test_cases):
            method = test_case['method']
            data = test_case['data']
            
            print(f"\n  测试 {i+1}: {method} 请求")
            if data:
                print(f"    参数: {json.dumps(data, ensure_ascii=False)}")
            
            try:
                if method == 'GET':
                    response = self.session.get(url, timeout=30)
                else:
                    response = self.session.post(url, json=data, timeout=30)
                
                print(f"    状态码: {response.status_code}")
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    print(f"    Content-Type: {content_type}")
                    
                    if 'application/json' in content_type:
                        try:
                            result = response.json()
                            print(f"    ✅ JSON响应成功!")
                            print(f"    响应数据: {json.dumps(result, ensure_ascii=False, indent=6)}")
                            
                            successful_calls.append({
                                'method': method,
                                'data': data,
                                'response': result,
                                'timestamp': datetime.now().isoformat()
                            })
                            
                        except json.JSONDecodeError:
                            print(f"    ⚠️ 响应不是有效JSON")
                            print(f"    响应内容: {response.text[:500]}")
                    else:
                        print(f"    ⚠️ 非JSON响应")
                        if len(response.text) < 1000:
                            print(f"    响应内容: {response.text}")
                        else:
                            print(f"    响应长度: {len(response.text)} 字符")
                
                elif response.status_code == 400:
                    print(f"    ⚠️ 请求错误 (400)")
                    try:
                        error = response.json()
                        print(f"    错误信息: {json.dumps(error, ensure_ascii=False, indent=6)}")
                    except:
                        print(f"    错误内容: {response.text[:200]}")
                
                elif response.status_code == 401:
                    print(f"    🔒 认证失败 (401)")
                
                elif response.status_code == 403:
                    print(f"    🚫 权限不足 (403)")
                
                elif response.status_code == 404:
                    print(f"    ❌ 端点不存在 (404)")
                
                elif response.status_code == 405:
                    print(f"    ❌ 方法不允许 (405)")
                
                else:
                    print(f"    ⚠️ 其他状态码: {response.status_code}")
                    try:
                        error_data = response.json()
                        print(f"    错误信息: {json.dumps(error_data, ensure_ascii=False, indent=6)}")
                    except:
                        print(f"    响应内容: {response.text[:200]}")
                
            except Exception as e:
                print(f"    ❌ 请求失败: {str(e)}")
        
        return successful_calls
    
    def explore_kiana_path(self):
        """探索/api/kiana路径下的其他API"""
        print(f"\n🔍 探索 /api/kiana 路径下的其他API...")
        
        # 基于已知路径的变体
        kiana_endpoints = [
            '/api/kiana/mms/robin/searchForSemiSupplier',  # 已知端点
            '/api/kiana/mms/robin/searchForSupplier',
            '/api/kiana/mms/robin/getSupplierList',
            '/api/kiana/mms/robin/getSupplierInfo',
            '/api/kiana/mms/robin/searchForProduct',
            '/api/kiana/mms/robin/getProductList',
            '/api/kiana/mms/supplier/list',
            '/api/kiana/mms/supplier/search',
            '/api/kiana/mms/supplier/info',
            '/api/kiana/mms/product/list',
            '/api/kiana/mms/product/search',
            '/api/kiana/mms/account/info',
            '/api/kiana/mms/store/info',
            '/api/kiana/mms/order/list'
        ]
        
        additional_successful = []
        
        for endpoint in kiana_endpoints:
            url = f"https://agentseller.temu.com{endpoint}"
            print(f"\n  测试: {endpoint}")
            
            try:
                # 尝试POST请求
                response = self.session.post(url, json={}, timeout=30)
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'application/json' in content_type:
                        try:
                            result = response.json()
                            print(f"    ✅ 成功!")
                            print(f"    响应: {json.dumps(result, ensure_ascii=False, indent=6)}")
                            
                            additional_successful.append({
                                'endpoint': endpoint,
                                'method': 'POST',
                                'data': {},
                                'response': result,
                                'timestamp': datetime.now().isoformat()
                            })
                            
                        except json.JSONDecodeError:
                            pass
                else:
                    print(f"    ❌ 状态码: {response.status_code}")
                
            except Exception as e:
                print(f"    ❌ 错误: {str(e)}")
        
        return additional_successful
    
    def save_results(self, all_results):
        """保存测试结果"""
        if not all_results:
            print("\n❌ 没有成功的API调用")
            return None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"kiana_api_results_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 结果已保存到: {filename}")
        return filename

def main():
    print("🚀 测试Kiana API端点...")
    
    tester = KianaAPITester()
    
    # 测试主要的API端点
    successful_calls = tester.test_kiana_api()
    
    # 探索相关的API端点
    additional_calls = tester.explore_kiana_path()
    
    # 合并所有成功的调用
    all_results = successful_calls + additional_calls
    
    if all_results:
        # 保存结果
        filename = tester.save_results(all_results)
        
        print(f"\n🎉 测试完成!")
        print(f"成功的API调用数量: {len(all_results)}")
        
        for result in all_results:
            if 'endpoint' in result:
                print(f"  ✅ {result['method']} {result['endpoint']}")
            else:
                method = result['method']
                data = result.get('data', {})
                print(f"  ✅ {method} /api/kiana/mms/robin/searchForSemiSupplier (参数: {data})")
        
        if filename:
            print(f"\n📁 结果文件: {filename}")
    else:
        print(f"\n❌ 没有成功的API调用")
        print("可能的原因:")
        print("1. Cookie已过期")
        print("2. API需要特定的参数格式")
        print("3. 需要额外的认证信息")

if __name__ == "__main__":
    main()
