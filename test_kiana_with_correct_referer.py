#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import os
from datetime import datetime

class KianaAPITesterFixed:
    def __init__(self):
        # 禁用代理
        os.environ.pop('HTTP_PROXY', None)
        os.environ.pop('HTTPS_PROXY', None)
        os.environ.pop('http_proxy', None)
        os.environ.pop('https_proxy', None)
        
        self.session = requests.Session()
        proxies = {'http': None, 'https': None}
        self.session.proxies.update(proxies)
        self.session.trust_env = False
        
        # 设置cookie
        cookie_string = "api_uid=Ct4kjmiNlzU98QBGDn3oAg==; _nano_fp=Xpmyn0golpC8XqT8X9_v~JX55DvIt6xeNJtDofAJ; _bee=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; njrpl=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; dilx=MchfnouEwE7NM0Un4s5iO; hfsc=L3yPfIgw4Dbw15/MeQ==; seller_temp=N_eyJ0IjoiUXlzUVZhMHlIMTcxb1hFemhoUlpMWlA5dkVoUzJoRlFiSjZxRTJOYWVldnFjZWt1aGU2cTV4VWsrY0ZqUWdJU2t5dEI1RVJkUjdXT0xJc21rWmhjVHc9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyNDI3ODU0ODg1MTkwMn0=; mallid=634418224371052"
        
        # 解析cookie字符串
        for cookie in cookie_string.split('; '):
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                self.session.cookies.set(name, value, domain='.temu.com')
                self.session.cookies.set(name, value, domain='.agentseller.temu.com')
        
        # 设置正确的请求头 - 使用正确的Referer
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://agentseller.temu.com/newon/product-select',  # 关键！使用正确的Referer
            'Origin': 'https://agentseller.temu.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Content-Type': 'application/json'
        })
    
    def test_kiana_api_with_correct_referer(self):
        """使用正确的Referer测试Kiana API"""
        url = "https://agentseller.temu.com/api/kiana/mms/robin/searchForSemiSupplier"
        
        print(f"🔍 使用正确Referer测试API: {url}")
        print(f"📍 Referer: https://agentseller.temu.com/newon/product-select")
        
        # 尝试不同的POST参数
        test_cases = [
            # 空参数
            {},
            
            # 搜索相关参数
            {'keyword': ''},
            {'search': ''},
            {'query': ''},
            {'searchText': ''},
            
            # 分页参数
            {'page': 1, 'size': 10},
            {'pageNum': 1, 'pageSize': 10},
            {'offset': 0, 'limit': 10},
            {'current': 1, 'pageSize': 10},
            
            # 供应商类型参数
            {'supplierType': 'semi'},
            {'type': 'semi'},
            {'category': 'all'},
            {'status': 'active'},
            
            # 可能的筛选参数
            {'filter': {}},
            {'filters': []},
            {'sort': 'default'},
            {'orderBy': 'default'},
            
            # 组合参数
            {
                'keyword': '',
                'page': 1,
                'size': 10,
                'supplierType': 'semi'
            },
            {
                'searchText': '',
                'pageNum': 1,
                'pageSize': 10,
                'type': 'semi',
                'status': 'active'
            },
            {
                'query': '',
                'current': 1,
                'pageSize': 20,
                'category': 'all',
                'sort': 'default'
            }
        ]
        
        successful_calls = []
        
        for i, data in enumerate(test_cases):
            print(f"\n  测试 {i+1}: POST 请求")
            print(f"    参数: {json.dumps(data, ensure_ascii=False)}")
            
            try:
                response = self.session.post(url, json=data, timeout=30)
                
                print(f"    状态码: {response.status_code}")
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    print(f"    Content-Type: {content_type}")
                    
                    if 'application/json' in content_type:
                        try:
                            result = response.json()
                            print(f"    ✅ JSON响应成功!")
                            print(f"    响应数据: {json.dumps(result, ensure_ascii=False, indent=6)}")
                            
                            successful_calls.append({
                                'endpoint': '/api/kiana/mms/robin/searchForSemiSupplier',
                                'method': 'POST',
                                'data': data,
                                'response': result,
                                'timestamp': datetime.now().isoformat()
                            })
                            
                        except json.JSONDecodeError:
                            print(f"    ⚠️ 响应不是有效JSON")
                            print(f"    响应内容: {response.text[:500]}")
                    else:
                        print(f"    ⚠️ 非JSON响应")
                        print(f"    响应内容: {response.text[:200]}")
                
                elif response.status_code == 400:
                    print(f"    ⚠️ 请求错误 (400)")
                    try:
                        error = response.json()
                        print(f"    错误信息: {json.dumps(error, ensure_ascii=False, indent=6)}")
                    except:
                        print(f"    错误内容: {response.text[:200]}")
                
                elif response.status_code == 403:
                    print(f"    🚫 权限不足 (403)")
                    try:
                        error = response.json()
                        print(f"    错误信息: {json.dumps(error, ensure_ascii=False, indent=6)}")
                    except:
                        print(f"    错误内容: {response.text[:200]}")
                
                else:
                    print(f"    ⚠️ 状态码: {response.status_code}")
                    try:
                        error_data = response.json()
                        print(f"    错误信息: {json.dumps(error_data, ensure_ascii=False, indent=6)}")
                    except:
                        print(f"    响应内容: {response.text[:200]}")
                
            except Exception as e:
                print(f"    ❌ 请求失败: {str(e)}")
        
        return successful_calls
    
    def explore_newon_related_apis(self):
        """探索与newon相关的其他API"""
        print(f"\n🔍 探索与newon相关的其他API...")
        
        # 基于newon/product-select页面可能调用的API
        newon_apis = [
            '/api/kiana/mms/robin/searchForSemiSupplier',  # 已知
            '/api/kiana/mms/robin/searchForSupplier',
            '/api/kiana/mms/robin/getSupplierList',
            '/api/kiana/mms/robin/getSupplierInfo',
            '/api/kiana/mms/robin/searchForProduct',
            '/api/kiana/mms/robin/getProductList',
            '/api/kiana/mms/robin/getProductInfo',
            '/api/kiana/mms/robin/getCategories',
            '/api/kiana/mms/robin/getCategoryList',
            '/api/kiana/mms/product/search',
            '/api/kiana/mms/product/list',
            '/api/kiana/mms/product/categories',
            '/api/kiana/mms/supplier/search',
            '/api/kiana/mms/supplier/list',
            '/api/newon/product/search',
            '/api/newon/product/list',
            '/api/newon/supplier/search',
            '/api/newon/supplier/list'
        ]
        
        additional_successful = []
        
        for endpoint in newon_apis:
            url = f"https://agentseller.temu.com{endpoint}"
            print(f"\n  测试: {endpoint}")
            
            try:
                # 使用正确的Referer
                response = self.session.post(url, json={}, timeout=30)
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'application/json' in content_type:
                        try:
                            result = response.json()
                            print(f"    ✅ 成功!")
                            print(f"    响应: {json.dumps(result, ensure_ascii=False, indent=6)}")
                            
                            additional_successful.append({
                                'endpoint': endpoint,
                                'method': 'POST',
                                'data': {},
                                'response': result,
                                'timestamp': datetime.now().isoformat()
                            })
                            
                        except json.JSONDecodeError:
                            pass
                else:
                    print(f"    ❌ 状态码: {response.status_code}")
                
            except Exception as e:
                print(f"    ❌ 错误: {str(e)}")
        
        return additional_successful
    
    def save_results(self, all_results):
        """保存测试结果"""
        if not all_results:
            print("\n❌ 没有成功的API调用")
            return None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"kiana_api_success_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 结果已保存到: {filename}")
        return filename

def main():
    print("🚀 使用正确Referer重新测试Kiana API...")
    
    tester = KianaAPITesterFixed()
    
    # 测试主要的API端点
    successful_calls = tester.test_kiana_api_with_correct_referer()
    
    # 如果主API成功，探索相关的API端点
    if successful_calls:
        print(f"\n🎉 主API测试成功！继续探索相关API...")
        additional_calls = tester.explore_newon_related_apis()
        successful_calls.extend(additional_calls)
    
    if successful_calls:
        # 保存结果
        filename = tester.save_results(successful_calls)
        
        print(f"\n🎉 测试完成!")
        print(f"成功的API调用数量: {len(successful_calls)}")
        
        for result in successful_calls:
            endpoint = result['endpoint']
            method = result['method']
            data = result.get('data', {})
            if data:
                print(f"  ✅ {method} {endpoint} (参数: {data})")
            else:
                print(f"  ✅ {method} {endpoint}")
        
        if filename:
            print(f"\n📁 结果文件: {filename}")
    else:
        print(f"\n❌ 仍然没有成功的API调用")
        print("建议:")
        print("1. 检查是否需要在浏览器中先访问 https://agentseller.temu.com/newon/product-select")
        print("2. 可能需要特定的会话状态")
        print("3. 查看浏览器中实际发送的请求参数")

if __name__ == "__main__":
    main()
