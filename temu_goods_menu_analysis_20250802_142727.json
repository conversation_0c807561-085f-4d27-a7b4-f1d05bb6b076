{"timestamp": "20250802_142727", "analysis_type": "goods_management_menu", "page_analyses": [{"route": "/goods", "url": "https://agentseller.temu.com/goods", "title": "<PERSON><PERSON> Central", "menu_items": [], "navigation_items": [], "button_actions": [], "form_fields": [], "table_headers": [], "api_endpoints": [], "component_names": []}, {"route": "/goods/list", "url": "https://agentseller.temu.com/goods/list", "title": "<PERSON><PERSON> Central", "menu_items": [], "navigation_items": [], "button_actions": [], "form_fields": [], "table_headers": [], "api_endpoints": [], "component_names": []}, {"route": "/goods-entry", "url": "https://agentseller.temu.com/goods-entry", "title": "Webpack App", "menu_items": [], "navigation_items": [], "button_actions": [], "form_fields": [], "table_headers": [], "api_endpoints": [], "component_names": []}, {"route": "/products", "url": "https://agentseller.temu.com/products", "title": "<PERSON><PERSON> Central", "menu_items": [], "navigation_items": [], "button_actions": [], "form_fields": [], "table_headers": [], "api_endpoints": [], "component_names": []}, {"route": "/product", "url": "https://agentseller.temu.com/product", "title": "<PERSON><PERSON> Central", "menu_items": [], "navigation_items": [], "button_actions": [], "form_fields": [], "table_headers": [], "api_endpoints": [], "component_names": []}, {"route": "/items", "url": "https://agentseller.temu.com/items", "title": "<PERSON><PERSON> Central", "menu_items": [], "navigation_items": [], "button_actions": [], "form_fields": [], "table_headers": [], "api_endpoints": [], "component_names": []}, {"route": "/customize-goods", "url": "https://agentseller.temu.com/customize-goods", "title": "<PERSON><PERSON> Central", "menu_items": [], "navigation_items": [], "button_actions": [], "form_fields": [], "table_headers": [], "api_endpoints": [], "component_names": []}, {"route": "/images-edit", "url": "https://agentseller.temu.com/images-edit", "title": "<PERSON><PERSON> Central", "menu_items": [], "navigation_items": [], "button_actions": [], "form_fields": [], "table_headers": [], "api_endpoints": [], "component_names": []}, {"route": "/data-center/goods-data", "url": "https://agentseller.temu.com/data-center/goods-data", "title": "<PERSON><PERSON> Central", "menu_items": [], "navigation_items": [], "button_actions": [], "form_fields": [], "table_headers": [], "api_endpoints": [], "component_names": []}, {"route": "/main/goods-label/make", "url": "https://agentseller.temu.com/main/goods-label/make", "title": "<PERSON><PERSON> Central", "menu_items": [], "navigation_items": [], "button_actions": [], "form_fields": [], "table_headers": [], "api_endpoints": [], "component_names": []}, {"route": "/main/business-opportunity/goods-detail", "url": "https://agentseller.temu.com/main/business-opportunity/goods-detail", "title": "<PERSON><PERSON> Central", "menu_items": [], "navigation_items": [], "button_actions": [], "form_fields": [], "table_headers": [], "api_endpoints": [], "component_names": []}, {"route": "/newon/product-select", "url": "https://agentseller.temu.com/newon/product-select", "title": "<PERSON><PERSON> Central", "menu_items": [], "navigation_items": [], "button_actions": [], "form_fields": [], "table_headers": [], "api_endpoints": [], "component_names": []}, {"route": "/visage-agent-seller/product/skc/pageQuery", "url": "https://agentseller.temu.com/visage-agent-seller/product/skc/pageQuery", "title": "<PERSON><PERSON> Central", "menu_items": [], "navigation_items": [], "button_actions": [], "form_fields": [], "table_headers": [], "api_endpoints": [], "component_names": []}, {"route": "/open/market/service/goods-rule/main", "url": "https://agentseller.temu.com/open/market/service/goods-rule/main", "title": "<PERSON><PERSON> Central", "menu_items": [], "navigation_items": [], "button_actions": [], "form_fields": [], "table_headers": [], "api_endpoints": [], "component_names": []}, {"route": "/open/market/service/goods-rule/order-detail", "url": "https://agentseller.temu.com/open/market/service/goods-rule/order-detail", "title": "<PERSON><PERSON> Central", "menu_items": [], "navigation_items": [], "button_actions": [], "form_fields": [], "table_headers": [], "api_endpoints": [], "component_names": []}, {"route": "/open/market/service/goods-rule/order-list", "url": "https://agentseller.temu.com/open/market/service/goods-rule/order-list", "title": "<PERSON><PERSON> Central", "menu_items": [], "navigation_items": [], "button_actions": [], "form_fields": [], "table_headers": [], "api_endpoints": [], "component_names": []}], "working_apis": [], "summary": {"total_pages_analyzed": 16, "total_working_apis": 0, "total_menu_items": 0, "total_navigation_items": 0, "total_button_actions": 0}}