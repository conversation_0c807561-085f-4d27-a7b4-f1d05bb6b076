[{"route": "/chat-app", "url": "https://agentseller.temu.com/chat-app", "status_code": 200, "content_type": "text/html; charset=utf-8", "title": "<PERSON><PERSON> Central"}, {"route": "/labor", "url": "https://agentseller.temu.com/labor", "status_code": 200, "content_type": "text/html; charset=utf-8", "title": "<PERSON><PERSON> Central"}, {"route": "/lgst", "url": "https://agentseller.temu.com/lgst", "status_code": 200, "content_type": "text/html; charset=utf-8", "title": "<PERSON><PERSON> Central"}, {"route": "/main", "url": "https://agentseller.temu.com/main", "status_code": 200, "content_type": "text/html; charset=utf-8", "title": "<PERSON><PERSON> Central"}, {"route": "/pmm/api/pmm/front_err", "url": "https://agentseller.temu.com/pmm/api/pmm/front_err", "status_code": 200, "content_type": "text/html; charset=utf-8", "title": "<PERSON><PERSON> Central"}, {"route": "/stock", "url": "https://agentseller.temu.com/stock", "status_code": 200, "content_type": "text/html; charset=utf-8", "title": "<PERSON><PERSON> Central"}]