#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import os
from datetime import datetime

class TemuSpecificAPITester:
    def __init__(self):
        # 禁用代理
        os.environ.pop('HTTP_PROXY', None)
        os.environ.pop('HTTPS_PROXY', None)
        os.environ.pop('http_proxy', None)
        os.environ.pop('https_proxy', None)
        
        self.session = requests.Session()
        proxies = {'http': None, 'https': None}
        self.session.proxies.update(proxies)
        self.session.trust_env = False
        
        # 设置cookie
        cookie_string = "api_uid=Ct4kjmiNlzU98QBGDn3oAg==; _nano_fp=Xpmyn0golpC8XqT8X9_v~JX55DvIt6xeNJtDofAJ; _bee=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; njrpl=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; dilx=MchfnouEwE7NM0Un4s5iO; hfsc=L3yPfIgw4Dbw15/MeQ==; seller_temp=N_eyJ0IjoiUXlzUVZhMHlIMTcxb1hFemhoUlpMWlA5dkVoUzJoRlFiSjZxRTJOYWVldnFjZWt1aGU2cTV4VWsrY0ZqUWdJU2t5dEI1RVJkUjdXT0xJc21rWmhjVHc9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyNDI3ODU0ODg1MTkwMn0=; mallid=634418224371052"
        
        # 解析cookie字符串并设置到不同域名
        for cookie in cookie_string.split('; '):
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                # 为不同的域名设置cookie
                self.session.cookies.set(name, value, domain='.temu.com')
                self.session.cookies.set(name, value, domain='.agentseller.temu.com')
                self.session.cookies.set(name, value, domain='.pftk.temu.com')
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Content-Type': 'application/json'
        })
    
    def test_api_endpoint(self, url, methods=['GET', 'POST']):
        """测试单个API端点"""
        print(f"\n🔍 测试API: {url}")
        
        results = {}
        
        for method in methods:
            try:
                print(f"  尝试 {method} 请求...")
                
                # 设置适当的Referer
                if 'agentseller.temu.com' in url:
                    self.session.headers['Referer'] = 'https://agentseller.temu.com/'
                    self.session.headers['Origin'] = 'https://agentseller.temu.com'
                elif 'pftk.temu.com' in url:
                    self.session.headers['Referer'] = 'https://us.pftk.temu.com/'
                    self.session.headers['Origin'] = 'https://us.pftk.temu.com'
                else:
                    self.session.headers['Referer'] = 'https://seller.temu.com/'
                    self.session.headers['Origin'] = 'https://seller.temu.com'
                
                if method == 'GET':
                    response = self.session.get(url, timeout=30)
                else:
                    # 尝试不同的POST数据格式
                    post_data_options = [
                        {},
                        {'page': 1, 'size': 10},
                        {'pageNum': 1, 'pageSize': 10},
                        {'offset': 0, 'limit': 10}
                    ]
                    
                    for post_data in post_data_options:
                        response = self.session.post(url, json=post_data, timeout=30)
                        if response.status_code == 200:
                            break
                
                print(f"    状态码: {response.status_code}")
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    print(f"    Content-Type: {content_type}")
                    
                    if 'application/json' in content_type:
                        try:
                            data = response.json()
                            print(f"    ✅ JSON响应成功!")
                            print(f"    响应数据: {json.dumps(data, ensure_ascii=False, indent=4)}")
                            results[method] = {
                                'success': True,
                                'data': data,
                                'status_code': response.status_code
                            }
                        except json.JSONDecodeError:
                            print(f"    ⚠️ 响应不是有效JSON")
                            print(f"    响应内容: {response.text[:500]}")
                            results[method] = {
                                'success': False,
                                'error': 'Invalid JSON',
                                'content': response.text[:500]
                            }
                    else:
                        print(f"    ⚠️ 非JSON响应")
                        if len(response.text) < 1000:
                            print(f"    响应内容: {response.text}")
                        else:
                            print(f"    响应长度: {len(response.text)} 字符")
                        results[method] = {
                            'success': False,
                            'error': 'Non-JSON response',
                            'content_type': content_type,
                            'content_length': len(response.text)
                        }
                
                elif response.status_code == 401:
                    print(f"    🔒 认证失败")
                    results[method] = {'success': False, 'error': 'Authentication failed'}
                
                elif response.status_code == 403:
                    print(f"    🚫 权限不足")
                    results[method] = {'success': False, 'error': 'Permission denied'}
                
                elif response.status_code == 404:
                    print(f"    ❌ 端点不存在")
                    results[method] = {'success': False, 'error': 'Not found'}
                
                else:
                    print(f"    ⚠️ 其他状态码: {response.status_code}")
                    try:
                        error_data = response.json()
                        print(f"    错误信息: {json.dumps(error_data, ensure_ascii=False, indent=4)}")
                        results[method] = {
                            'success': False,
                            'error': error_data,
                            'status_code': response.status_code
                        }
                    except:
                        print(f"    响应内容: {response.text[:200]}")
                        results[method] = {
                            'success': False,
                            'error': response.text[:200],
                            'status_code': response.status_code
                        }
                
            except Exception as e:
                print(f"    ❌ 请求失败: {str(e)}")
                results[method] = {'success': False, 'error': str(e)}
        
        return results
    
    def test_all_endpoints(self):
        """测试所有提供的端点"""
        endpoints = [
            'https://agentseller.temu.com/api/bg-ladyfish/mms/menu/page/feedback/entrance',
            'https://agentseller.temu.com/quick/merchant/pop/query',
            'https://us.pftk.temu.com/pmm/api/pmm/api'
        ]
        
        all_results = {}
        
        for endpoint in endpoints:
            results = self.test_api_endpoint(endpoint)
            all_results[endpoint] = results
        
        return all_results
    
    def explore_agentseller_domain(self):
        """探索agentseller.temu.com域名下的其他API"""
        print(f"\n🌐 探索 agentseller.temu.com 域名...")
        
        # 基于第一个成功的API路径，尝试相关的端点
        base_endpoints = [
            'https://agentseller.temu.com/api/bg-ladyfish/mms/menu/page/feedback/entrance',
            'https://agentseller.temu.com/api/bg-ladyfish/mms/menu/list',
            'https://agentseller.temu.com/api/bg-ladyfish/mms/account/info',
            'https://agentseller.temu.com/api/bg-ladyfish/mms/store/info',
            'https://agentseller.temu.com/api/bg-ladyfish/mms/product/list',
            'https://agentseller.temu.com/api/bg-ladyfish/mms/order/list',
            'https://agentseller.temu.com/quick/merchant/info',
            'https://agentseller.temu.com/quick/merchant/dashboard',
            'https://agentseller.temu.com/quick/store/info',
            'https://agentseller.temu.com/quick/product/list',
            'https://agentseller.temu.com/quick/order/list'
        ]
        
        additional_results = {}
        
        for endpoint in base_endpoints:
            print(f"\n探索: {endpoint}")
            results = self.test_api_endpoint(endpoint, methods=['GET', 'POST'])
            if any(result.get('success') for result in results.values()):
                additional_results[endpoint] = results
                print(f"✅ 发现有效端点: {endpoint}")
        
        return additional_results
    
    def save_results(self, results):
        """保存测试结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"temu_api_test_results_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 测试结果已保存到: {filename}")
        return filename

def main():
    print("🚀 开始测试指定的Temu API端点...")
    
    tester = TemuSpecificAPITester()
    
    # 测试指定的端点
    print("📋 测试用户提供的API端点...")
    results = tester.test_all_endpoints()
    
    # 如果发现有效的agentseller端点，探索更多
    agentseller_success = any(
        any(result.get('success') for result in endpoint_results.values())
        for url, endpoint_results in results.items()
        if 'agentseller.temu.com' in url
    )
    
    if agentseller_success:
        print("\n🎉 发现agentseller.temu.com有有效端点，继续探索...")
        additional_results = tester.explore_agentseller_domain()
        results.update(additional_results)
    
    # 保存结果
    filename = tester.save_results(results)
    
    # 显示摘要
    print(f"\n📊 测试摘要:")
    successful_endpoints = []
    for url, endpoint_results in results.items():
        for method, result in endpoint_results.items():
            if result.get('success'):
                successful_endpoints.append(f"{method} {url}")
                print(f"✅ {method} {url}")
    
    if successful_endpoints:
        print(f"\n🎉 总共发现 {len(successful_endpoints)} 个有效的API端点!")
    else:
        print(f"\n❌ 未发现有效的API端点")
        print("可能的原因:")
        print("1. Cookie已过期")
        print("2. 需要额外的认证参数")
        print("3. API端点已变更")

if __name__ == "__main__":
    main()
