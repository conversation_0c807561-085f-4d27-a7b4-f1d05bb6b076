{"timestamp": "20250802_141910", "discovered_routes": ["/open/market/service/logistics/main", "//", "/intllgst/", "/chat-app", "/hawk/mms/course/exam/queryCourseExam", "/newon", "/api/kiana/mms/gmp/bg/magneto/api/price/re-price-review-next-page", "/visage-agent-seller/product/skc/pageQuery", "/_VBA_PROJECT_CUR/", "/chat-app/contact-buyer", "/images-edit", "/api/pmm/front_err", "/mms/mink/freight/template/edit", "/crop/x", "/activity/marketing-activity/mdeast-subsidy", "/mms/otter/warehouse/popup/query_copywriting", "/material/guide/edit", "/api/kiana/magnus/mms/price/adjust/reduce-popup-next-page", "/thumbnail/60x", "/bert/api/page/info/logistics/pageInfo", "/api/galerie/cos_large_file/upload_complete", "/api/galerie/v3/store_image", "/8", "/01/01", "/activity", "/marvel-mms/us/api/kiana/direnjie/high/price/flow/reduce/confirmFullProductSkuAdjust", "/api/kiana/gamblers/marketing/coupon/invite/query", "/tai/api/heartbeat/key", "/main/survey/edit", "/open/market/service/rule/order-list", "/mall-transfer-compliance", "/api/galerie/file/signature", "/main/authentication", "/govern-platform/mobile-device-code/agreement", "/activity/marketing-tools/create-coupon", "/bert/api/page/info/tms/pageInfo", "/stock/fully-mgt/order-manage-custom", "/govern-platform", "/1", "/api/seller/frontend/report/uin/queryFrontendReportUinResult", "/api/kiana/firestar/unsold/stock/queryMallWaitHandleTask", "/bert/api/page/info/ams/pageInfo", "/Book", "/rradius/", "/api/seller/auth/userInfo", "/c/api/auth/c/token/obtainTicket", "/agora/conv/needReplyCount", "/bert/api/page/info/workbench/agent/pageInfo", "/chat-app/upload-template", "/open/market/service/video/order-list", "/marvel-mms/us/api/kiana/direnjie/high/price/flow/reduce/queryFullHighPriceFlowReduceIndexPopup", "/labor/credit-card-paying", "/api/kiana/magnus/mms/price/re-price-review/click", "/api/bg-ladyfish/mms/menu/page/feedback/submit", "/api/kiana/gamblers/marketing/enroll/pop/apply", "/bg/staff/api/user/permission/outsource/queryRoleMenuInfo", "/bert/api/page/info/agentSeller/pageInfo", "/cgif/", "/acw/shipping-mgt/details", "/survey", "/darwin-mms/api/kiana/ghost/btg/sales/stock/queryMmsBtgProductStockBaseInfo", "/api/bg/cw/pop/up/queryPopUpInfo", "/main/market-analysis", "/get-leo-config", "/open-entry", "/stock-entry", "/get_endpoint", "/api/bg/vision/mms/popup/query/relate_and_shipping_mode_sku/list", "/garen/mms/afterSales/count", "/text/", "/api/phantom/user_verify", "/sharpen/", "/api/pmm/front_log", "/open", "/gambit/api/mallApply/oneKeyOpenSemiMangedMall", "/mall/entity-info", "/api/bg/cw/mallCwTokenAuthorization", "/top-sale-high-yield", "/lgst/auth-warehouse/assembly-freight-estimate/result", "/bg/quick/api/merchant/rule/unreadList", "/restock/stocking-shipping-receipt-details", "/lollipop/gray/agent/seller/batchMatchBySupplierIdsWithMulGray", "/gambit/api/foreign/aml/execute/annualConfirm", "/lgst/auth-warehouse/order-rule-config/set-logistics-detail", "/main/business-opportunity/goods-detail", "/bg-ac-austin/web/api/mms/skuDiffConfirm/queryWaitProcessCount", "/cut/", "/rquality/", "/ams-lgst-temu/", "/main/flow-grow", "/labor-entry", "/api/kiana/firestar/unsold/stock/supplierConfirmUnsoldStockHandleMethod", "/darwin-mms/api/kiana/ghost/btg/sales/stock/queryUpdateMmsBtgProductSalesStockCondition", "/api/seller/auth/logout", "/darwin-mms/api/kiana/ghost/btg/sales/stock/popup/markOperateFinish", "/wms-entry", "/bert/api/page/info/app/pageInfo", "/message", "/api/galerie/large_file/v1/video/upload_complete", "/api/kiana/mms/gmp/bg/magneto/api/price-adjust/adjust-order-batch-confirm", "/main/course/detail", "/gambit/api/agreement/center/batchSignCenterAgreement", "/lq/", "/bert/api/page/info/oms/pageInfo", "/lgst/auth-warehouse/waybill-rule-configuration/rule-maintenance", "/main/evaluate/evaluate-list", "/gambit/api/mallApply/querySettleProtocol", "/main/category-analysis", "/open/market/service/rule/order-detail", "/bg/quick/api/merchant/msgBox/totalUnreadMsgNum", "/bg/quick/api/merchant/rule/unreadNum", "/settle/site-main", "/mms/mink/freight/template/pop/content/template_support_new_country", "/quick/merchant/pop/query", "/fill/", "/galerie/business/get_signature", "/api/bg/vision/mms/popup/query/content", "/antman/api/flow/commit", "/bert/api/page/info/rank/pageInfo", "/bg/staff/api/user/permission/tms/queryRoleMenuInfo", "/api/bg/vision/mms/popup/query/related_erp_list", "/feedback_v2/mms/api/create/black/and/white/list", "/quick/merchant/pop/read", "/general_auth/get_signature", "/activity/marketing-activity/semi-detail", "/quality/", "/bert/api/page/info/wms/pageInfo", "/marvel-mms/us/api/kiana/direnjie/high/price/flow/reduce/querySamePairAppealInfo", "/degree/", "/main/examination/process", "/antman/api/flow/checkActivityPrice", "/activity/marketing-activity/spring-activity", "/open/market/service/graph/main", "/copyright-guide", "/api/galerie/large_file/v1/video/upload_part", "/api/kiana/gamblers/marketing/config/queryMallCanChoosePriceType", "/api/galerie/image/signature", "/api/kiana/mms/gmp/bg/magneto/api/ams/price/priceAdjust/reducePricePopupNextPageV2", "/blur/", "/mode/1/ignore-error/", "/api/kiana/magnus/mms/price/re-price-review-next-page", "/mms/venom/api/supplier/merge/operate/editMergeOperateConfig", "/rotate/", "/kirogi/bg/mms/savePopupByCode", "/mms/venom/api/supplier/merge/operate/queryMergeOperateConfig", "/api/kiana/magnus/mms/priceAdjust/grayReducePricePopup", "/dissolve/", "/open/mall-decoration/editor", "/activity/marketing-activity/log", "/api/kiana/magnus/mms/price/adjust/price-reduce-popup-click", "/mall/account-info", "/survey/edit", "/lquality/", "/hawk/mms/course/exam/courseExamPopup", "/api/kiana/mms/magneto/price/query-compare-sku-info", "/main", "/activity/marketing-activity/detail-new", "/open-platform/system-manage/authorize", "/restock/stocking-shipping-receipt/operate", "/kirogi/bg/mms/subsidyDetails", "/api/server/_stm", "/api/galerie/cos_large_file/upload_part", "/activity/marketing-activity", "/tai/api/heartbeat/hit", "/stock-edit", "/api/kiana/gamblers/marketing/coupon/min/amount", "/api/kiana/fenrir/FullChanceGoodsInvitationMmsService/fullChanceInvitationPop", "/sample-entry", "/goods/list", "/api/kiana/mms/gmp/bg/magneto/api/price/adjust/price-reduce-popup-click", "/api/kiana/antman/adjust/for/flow/pop", "/417", "/hawk/mms/course/exam/queryCourseExamLogDetail", "/api/kiana/mms/gmp/bg/magneto/api/ams/price/priceAdjust/gmpReducePricePopupWithAmsV2", "/create/category", "/sample", "/goods", "/open-platform", "/api/kiana/magnus/mms/price/re-price-review", "/activity/marketing-tools/create-mall-coupon", "/activity-entry", "/labor/tax-settle", "/restock/stocking-shipping-receipt/calculate", "/bg/swift/api/auth/obtainCode", "/darwin-mms/api/kiana/ghost/btg/sales/stock/popup/queryNoStockProduct", "/api/bg/cw/pop/up/saveRecommendUseErpInfo", "/bg/staff/api/user/permission/lms/queryUserMenu", "/entity-edit-compliance", "/api/kiana/app/magneto/price-adjust/gmpReducePricePopupGray", "/api/pmm/api", "/newon-entry", "/activity/marketing-activity/hot-girl-cloth", "/darwin-mms/api/kiana/ghost/btg/sales/stock/updateMmsBtgProductSalesStock", "/api/kiana/mms/gmp/bg/magneto/api/price/re-price-review/click", "/bg/staff/api/user/permission/wms/queryRoleMenuInfo", "/bg/detroit/api/infoTicket/searchTicket", "/fontsize/", "/main/flux-analysis", "/api/bg/cw/getCwProviderList", "/api/pmm/static", "/api/kiana/gamblers/marketing/enroll/pop/product/list", "/api/kiana/mms/gmp/bg/magneto/api/price/re-price-review", "/api/kiana/magnus/mms/price/priceAdjust/gmpReducePricePopup", "/_VBA_PROJECT_CUR/VBA/dir", "/mms/mink/freight/template/all_market_region", "/api/galerie/public/signature", "/open/market/service/goods-rule/main", "/bert/api/page/info/vms/pageInfo", "/acw/shipping-mgt/operate", "/api/bg-ladyfish/mms/menu/page/feedback/entrance", "/rule-center", "/api/seller/auth/loginByCode", "/kirogi/bg/mms/homepage/queryPopup", "/kirogi/bg/mms/queryPopupList", "/419", "/newon/product-select", "/node_modules/gulp-browserify/node_modules/base64-js/lib", "/chat-app/service-indicator", "/main/business-opportunity", "/customize-goods", "/main/aftersales/information", "/rq/", "/open/market/service/video/order-create", "/main/goods-label/make", "/stock/fully-mgt/order-manage", "/bg/staff/api/user/permission/logistics/queryRoleMenuInfo", "/crop/", "/Workbook", "/kirogi/bg/mms/statisticWithType", "/open/market/service/rule/main", "/api/bg/vision/mms/gray/query", "/ignore-error/", "/activity/marketing-activity/detail-new-result", "/main-entry", "/api/kiana/magnus/mms/price/reviewDetail", "/restock/order-manage", "/interlace/", "/activity/marketing-activity/result", "/416", "/auto-orient", "/stock", "/api/galerie/general_file", "/node_modules/gulp-browserify/node_modules/crypto-browserify", "/marvel-mms/us/api/kiana/direnjie/high/price/flow/reduce/full/querySiteTargetPrice", "/encryption", "/goods-entry", "/bert/api/page/info/gmp/pageInfo", "/govern-platform/entry", "/activity/marketing-tools/create-recommend-coupon", "/open-platform/entry", "/EncryptedPackage", "/mms/otter/warehouse/query", "/activity/marketing-activity/exposure-subsidy-detail", "/entity-replace-compliance", "/bert/api/page/info/foreignTms/pageInfo", "/stock/fully-mgt/production-suggestion", "/kirogi/bg/mms/queryPopupConfig", "/material", "/api/charge_back/query/status/count", "/thumbnail", "/darwin-mms/api/kiana/ghost/btg/sales/stock/popup/submitPredictReceiveStock", "/format/webp", "/412", "/feedback_v2/mms/api/list/not/same/reason", "/api/pmm/defined", "/restock/plan", "/bg/quick/api/merchant/rule/read", "/api/galerie/large_file/v1/video/upload_init", "/node_modules/gulp-browserify/node_modules/buffer", "/api/bg/cw/getCwTicket", "/api/kiana/magnus/mms/priceAdjust/grayReducePricePopupNextPage", "/acw/stocking-mgt/operate", "/EncryptionInfo", "/gambit/api/puWinCenter/queryPopUpWindowDynamicContent", "/bg/staff/api/user/permission/workbench/queryRoleMenuInfo", "/main/course/list", "/hawk/mms/course/exam/submitCourseExamLog", "/main/evaluate-manage", "/mms/venom/api/supplier/sales/management/queryInviteJitSkc", "/scrop/", "/feedback_v2/mms/api/create/black/and/white/batchCreateSamePair", "/c/api/auth/c/token/showJumpTemu", "/main/business-opportunity/market-detail", "/knowledge-guide", "/bg/staff/api/user/permission/oms/queryRoleMenuInfo", "/bg/staff/api/user/permission/vms/queryRoleMenuInfo", "/edit", "/gravity/northeast/batch/1/degree/", "/customer-consultation", "/bg/swift/api/jump/token", "/api/kiana/fenrir/CompetitorGoodsInvitationMmsService/cmpGoodsMmsPage", "/api/pmm/page", "/api/bg/vision/mms/product/sku/relate/batch_switch_shipping_mode", "/mmsos/orders", "/lgst/auth-warehouse/order-rule-config/manually-review-orders-detail", "/api/phantom/", "/open/market/service/goods-rule/order-list", "/chat-app/index", "/dy/", "/api/galerie/v1/store_video", "/portal/", "/authentication", "/api/kiana/mms/magneto/price/reviewDetail", "/activity/marketing-activity/auto-sign", "/gambit/api/puWinCenter/queryPopUpWindow", "/a/th", "/daredevil/api/purchase/order/queryPurchaseOrderStatusCount", "/bert/api/page/info/workbench/pageInfo", "/wms", "/acw/stocking-mgt/details", "/open/market/service/goods-rule/order-detail", "/chat-app/ticket-detail", "/api/kiana/firestar/unsold/stock/pageQueryMallWaitHandleTaskUnsoldProduct", "/open/market/service/all", "/mms/mink/freight/template/protocol/free_shipping/batch/edit", "/thumbnail/x", "/grayscale/1", "/stock/fully-mgt/order-manage-urgency", "/bert/api/page/info/lms/pageInfo", "/labor", "/3", "/main/survey", "/api/kiana/magnus/mms/price/query-compare-sku-info", "/data-center/goods-data", "/check-compliance", "/bg/quick/api/merchant/msgBox/unreadMsgDetail", "/mms/mink/freight/template/config", "/material-entry", "/api/kiana/gamblers/marketing/enroll/pop/activity/list", "/dx/", "/w/", "/q/", "/>", "/mms/mink/freight/template/pop/content/template_delivery_time", "/api/pmm", "/font/", "/marvel-mms/us/api/kiana/direnjie/high/price/flow/reduce/confirmSemiProductSkuAdjust", "/iradius/", "/penalty-temu/", "/node_modules/gulp-browserify/node_modules/process", "/top-sale-cate", "/bert/api/page/info/foreignLogistics/pageInfo", "/marvel-mms/us/api/kiana/direnjie/high/price/flow/reduce/semi/querySemiHighPriceReduceIndexPopup", "/api/seller/auth/menu", "/h/", "/bg/staff/api/foreign/logistics/account/menu", "/activity/marketing-activity/detail/worry-free", "/x", "/node_modules/gulp-browserify/node_modules/ieee754", "/gravity/", "/gravity/center", "/api/kiana/mms/gmp/bg/magneto/api/ams/price/priceAdjust/gmpReducePricePopupV2", "/api/phantom/captcha_unsupport", "/api/bg/vision/mms/product/sku/match/bound/submit", "/open/market/service/material/main", "/bg/quick/api/merchant/msgBox/read", "/api/kiana/mms/gamblers/bidding/womenSwearBiddingInvitationPopList", "/bg/staff/api/user/permission/agent/service/portal/queryRoleMenuInfo", "/thumbnail/", "/api/bg/cw/redpoint", "/bg/staff/api/user/permission/sc/queryRoleMenuInfo", "/api/phantom/obtain_captcha", "/mms/otter/warehouse/popup/query_popup", "/chat-entry", "/mms/venom/api/supplier/sales/management/convertToJit", "/chat-app/ticket-list", "/api/bg/vision/mms/product/sku/relate/query/shipping_mode_product_skc", "/batch/1", "/main/examination/detail", "/lgst", "/labor/tax-document-library", "/api/galerie/cos_large_file/upload_init", "/top-sale-stock", "/format/", "/agent-seller-lgst-entry"], "discovered_apis": ["/api/kiana/mms/gmp/bg/magneto/api/price/re-price-review/click", "/api/kiana/mms/gmp/bg/magneto/api/price/re-price-review-next-page", "/api/bg/cw/getCwProviderList", "/api/pmm/front_err", "/api/pmm/static", "/api/kiana/gamblers/marketing/enroll/pop/product/list", "/api/kiana/magnus/mms/price/adjust/reduce-popup-next-page", "/api/kiana/mms/gmp/bg/magneto/api/price/re-price-review", "/api/bg/vision/mms/popup/query/content", "/api/kiana/magnus/mms/price/priceAdjust/gmpReducePricePopup", "/api/bg/vision/mms/popup/query/related_erp_list", "/api/galerie/public/signature", "/api/kiana/fenrir/CompetitorGoodsInvitationMmsService/cmpGoodsMmsPage", "/api/galerie/cos_large_file/upload_complete", "/api/galerie/v3/store_image", "/api/pmm/page", "/api/bg/vision/mms/product/sku/relate/batch_switch_shipping_mode", "/api/phantom/", "/api/galerie/v1/store_video", "/api/kiana/gamblers/marketing/coupon/invite/query", "/api/bg-ladyfish/mms/menu/page/feedback/entrance", "/api/kiana/mms/magneto/price/reviewDetail", "/api/seller/auth/loginByCode", "/api/galerie/file/signature", "/api/galerie/large_file/v1/video/upload_part", "/api/kiana/gamblers/marketing/config/queryMallCanChoosePriceType", "/api/seller/frontend/report/uin/queryFrontendReportUinResult", "/api/galerie/image/signature", "/api/kiana/mms/gmp/bg/magneto/api/ams/price/priceAdjust/reducePricePopupNextPageV2", "/api/kiana/firestar/unsold/stock/queryMallWaitHandleTask", "/api/kiana/firestar/unsold/stock/pageQueryMallWaitHandleTaskUnsoldProduct", "/api/kiana/magnus/mms/price/re-price-review-next-page", "/api/seller/auth/userInfo", "/api/kiana/magnus/mms/price/query-compare-sku-info", "/api/kiana/magnus/mms/priceAdjust/grayReducePricePopup", "/api/bg/vision/mms/gray/query", "/api/kiana/magnus/mms/price/re-price-review/click", "/api/bg-ladyfish/mms/menu/page/feedback/submit", "/api/kiana/gamblers/marketing/enroll/pop/apply", "/api/kiana/magnus/mms/price/reviewDetail", "/api/kiana/gamblers/marketing/enroll/pop/activity/list", "/api/kiana/magnus/mms/price/adjust/price-reduce-popup-click", "/api/pmm", "/api/kiana/mms/magneto/price/query-compare-sku-info", "/api/bg/cw/pop/up/queryPopUpInfo", "/api/server/_stm", "/api/galerie/general_file", "/api/galerie/cos_large_file/upload_part", "/api/seller/auth/menu", "/api/bg/vision/mms/popup/query/relate_and_shipping_mode_sku/list", "/api/kiana/gamblers/marketing/coupon/min/amount", "/api/phantom/user_verify", "/api/kiana/fenrir/FullChanceGoodsInvitationMmsService/fullChanceInvitationPop", "/api/kiana/mms/gmp/bg/magneto/api/price/adjust/price-reduce-popup-click", "/api/pmm/front_log", "/api/kiana/antman/adjust/for/flow/pop", "/api/kiana/mms/gmp/bg/magneto/api/ams/price/priceAdjust/gmpReducePricePopupWithAmsV2", "/api/bg/cw/mallCwTokenAuthorization", "/api/kiana/mms/gmp/bg/magneto/api/ams/price/priceAdjust/gmpReducePricePopupV2", "/api/phantom/captcha_unsupport", "/api/bg/vision/mms/product/sku/match/bound/submit", "/api/kiana/mms/gamblers/bidding/womenSwearBiddingInvitationPopList", "/api/charge_back/query/status/count", "/api/bg/cw/redpoint", "/api/phantom/obtain_captcha", "/api/kiana/magnus/mms/price/re-price-review", "/api/bg/vision/mms/product/sku/relate/query/shipping_mode_product_skc", "/api/kiana/firestar/unsold/stock/supplierConfirmUnsoldStockHandleMethod", "/api/seller/auth/logout", "/api/pmm/defined", "/api/galerie/large_file/v1/video/upload_complete", "/api/bg/cw/pop/up/saveRecommendUseErpInfo", "/api/galerie/large_file/v1/video/upload_init", "/api/kiana/mms/gmp/bg/magneto/api/price-adjust/adjust-order-batch-confirm", "/api/bg/cw/getCwTicket", "/api/kiana/magnus/mms/priceAdjust/grayReducePricePopupNextPage", "/api/galerie/cos_large_file/upload_init", "/api/kiana/app/magneto/price-adjust/gmpReducePricePopupGray", "/api/pmm/api"], "menu_items": [{"title": "数据视图", "path": ""}, {"title": "保存为图片", "path": ""}, {"title": "这是一个关于“{title}”的图表。", "path": ""}, {"title": "这是一个图表，", "path": ""}, {"title": "Data View", "path": ""}, {"title": "Rest<PERSON>", "path": ""}, {"title": "Save as Image", "path": ""}, {"title": "This is a chart about", "path": ""}, {"title": "This is a chart", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "block-title-module__title___3MkQp", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "body-module__title___2ygLv", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "index-module__title___MqE4S", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "fields-hex-checker", "path": ""}, {"title": "<PERSON> Hex <PERSON>er (fieldsHash:", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "ExtendDeliveryTimeModal_columnTitle__zcGfl", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "index-module__title___Lfs1v", "path": ""}, {"title": "index-module__title___4e2lO", "path": ""}, {"title": "index-module__title___1HWeg", "path": ""}, {"title": "index-module__panelTitle___2i4lv", "path": ""}, {"title": "开始时间", "path": ""}, {"title": "结束时间", "path": ""}, {"title": "Title", "path": ""}, {"title": "label", "path": ""}, {"title": "label", "path": ""}, {"title": "index-module__row_label___1f9FT", "path": ""}, {"title": "label", "path": ""}, {"title": "beast-core-tab-itemLabel", "path": ""}, {"title": "line", "path": ""}, {"title": "label", "path": ""}, {"title": "label", "path": ""}, {"title": "label", "path": ""}, {"title": "label", "path": ""}, {"title": "beast-core-tab-itemLabel", "path": ""}, {"title": "line", "path": ""}, {"title": "index-module__label___bcQq-", "path": ""}, {"title": "pmm/offlineCMTStore", "path": ""}, {"title": "pmm-statistics", "path": ""}, {"title": "图表类型是{seriesType}，表示{seriesName}。", "path": ""}, {"title": "图表类型是{seriesType}。", "path": ""}, {"title": "第{seriesId}个系列是一个表示{seriesName}的{seriesType}，", "path": ""}, {"title": "第{seriesId}个系列是一个{seriesType}，", "path": ""}, {"title": "{name}的数据是{value}", "path": ""}, {"title": "{value}", "path": ""}, {"title": "with type {seriesType} named {seriesName}.", "path": ""}, {"title": "with type {seriesType}.", "path": ""}, {"title": "The {seriesId} series is a {seriesType} representing {seriesName}.", "path": ""}, {"title": "The {seriesId} series is a {seriesType}.", "path": ""}, {"title": "the data for {name} is {value}", "path": ""}, {"title": "{value}", "path": ""}, {"title": "line", "path": ""}, {"title": "main", "path": ""}, {"title": "main", "path": ""}, {"title": "main", "path": ""}, {"title": "min", "path": ""}, {"title": "median", "path": ""}, {"title": "max", "path": ""}, {"title": "open", "path": ""}, {"title": "close", "path": ""}, {"title": "lowest", "path": ""}, {"title": "highest", "path": ""}, {"title": "time", "path": ""}, {"title": "value", "path": ""}, {"title": "name", "path": ""}, {"title": "time", "path": ""}, {"title": "magicType", "path": ""}, {"title": "value", "path": ""}, {"title": "value", "path": ""}, {"title": "value", "path": ""}, {"title": "value", "path": ""}, {"title": "vertical", "path": ""}, {"title": "horizontal", "path": ""}, {"title": "pageText", "path": ""}, {"title": "rox-charts-for-react", "path": ""}, {"title": "AccessToken", "path": ""}, {"title": "AccessToken", "path": ""}, {"title": "PASSID", "path": ""}, {"title": "error-screen-wrapper", "path": ""}, {"title": "fail-title", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "dot-module__dot___3aiHD", "path": ""}, {"title": "dot-module__circle___2OJvm", "path": ""}, {"title": "dot-title-module__dot-title___3E7Lj", "path": ""}, {"title": "dot-title-module__left___3Zvsq", "path": ""}, {"title": "dot-title-module__circle___3_W4Z", "path": ""}, {"title": "dot-title-module__sub-title___2hoku", "path": ""}, {"title": "label-value-module__label-value___3DKbo", "path": ""}, {"title": "tag-module__tag___XnPMs", "path": ""}, {"title": "error-screen-wrapper", "path": ""}, {"title": "fail-title", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "popover", "path": ""}, {"title": "modal", "path": ""}, {"title": "error-screen-wrapper", "path": ""}, {"title": "fail-title", "path": ""}, {"title": "$rcNotificationFadeIn", "path": ""}, {"title": "$rcDialogFadeOut", "path": ""}, {"title": "beast-click-notice-", "path": ""}, {"title": "cookie", "path": ""}, {"title": "querystring", "path": ""}, {"title": "localStorage", "path": ""}, {"title": "sessionStorage", "path": ""}, {"title": "navigator", "path": ""}, {"title": "htmlTag", "path": ""}, {"title": "path", "path": ""}, {"title": "subdomain", "path": ""}, {"title": "error-screen-wrapper", "path": ""}, {"title": "fail-title", "path": ""}, {"title": "$rcNotificationFadeIn", "path": ""}, {"title": "$rcDialogFadeOut", "path": ""}, {"title": "beast-click-notice-", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "string", "path": ""}, {"title": "string", "path": ""}, {"title": "index-module__colon___2Pj__", "path": ""}, {"title": "index-module__itemColumnValue___3MOIz", "path": ""}, {"title": "index-module__mobileColumn___3Okwp", "path": ""}, {"title": "index-module__mobile___2BQTB", "path": ""}, {"title": "index-module__listWrapper___n6vCK", "path": ""}, {"title": "index-module__item___2n1-T", "path": ""}, {"title": "index-module__content___EeTQi", "path": ""}, {"title": "index-module__wrapper___VeiOR", "path": ""}, {"title": "index-module__dot___1Akrx", "path": ""}, {"title": "index-module__text___ZpPEu", "path": ""}, {"title": "index-module__wrap___1LbOT", "path": ""}, {"title": "index-module__text___3uAJJ", "path": ""}, {"title": "index-module__operate___2eLlI", "path": ""}, {"title": "index-module__wrapper___1xdTm", "path": ""}, {"title": "index-module__easySelectGroup___2AbIP", "path": ""}, {"title": "error-screen-wrapper", "path": ""}, {"title": "fail-title", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "index-module__withIconContent___1wh3i", "path": ""}, {"title": "index-module__actions___3hsBQ", "path": ""}, {"title": "index-module__confirmPopover___2XpzB", "path": ""}, {"title": "error-screen-wrapper", "path": ""}, {"title": "fail-title", "path": ""}, {"title": "index-module__drawer-body___1Ysov", "path": ""}, {"title": "index-module__title___1Tkj0", "path": ""}, {"title": "index-module__title-text___1RG0F", "path": ""}, {"title": "index-module__content___21iRG", "path": ""}, {"title": "index-module__footer___3e-rb", "path": ""}, {"title": "index-module__mask___1zIA2", "path": ""}, {"title": "error-screen-wrapper", "path": ""}, {"title": "fail-title", "path": ""}, {"title": "$rcNotificationFadeIn", "path": ""}, {"title": "$rcDialogFadeOut", "path": ""}, {"title": "beast-click-notice-", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "index-module__img___QYwgh", "path": ""}, {"title": "index-module__text___1rQw7", "path": ""}, {"title": "error-screen-wrapper", "path": ""}, {"title": "fail-title", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "string", "path": ""}, {"title": "string", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "label-value-module__label-value___1aG2L", "path": ""}, {"title": "error-screen-wrapper", "path": ""}, {"title": "fail-title", "path": ""}, {"title": "$rcNotificationFadeIn", "path": ""}, {"title": "$rcDialogFadeOut", "path": ""}, {"title": "beast-click-notice-", "path": ""}, {"title": "index-module__fold-btn___MG0Vw", "path": ""}, {"title": "index-module__fold-btn-num___J0HgH", "path": ""}, {"title": "index-module__fold-btn-text___2np8j", "path": ""}, {"title": "index-module__twf-fields___2_RYg", "path": ""}, {"title": "index-module__twf-form-wrap___fHB0M", "path": ""}, {"title": "index-module__col-query-btn___1Wrgy", "path": ""}, {"title": "index-module__col-query-btn-wrapper___1wERr", "path": ""}, {"title": "error-screen-wrapper", "path": ""}, {"title": "fail-title", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "bg-captcha-refresh-icon", "path": ""}, {"title": "bg-captcha-wrapper bg-captcha-wrapper-picture", "path": ""}, {"title": "bg-captcha-picture-code", "path": ""}, {"title": "bg-captcha-source", "path": ""}, {"title": "bg-captcha-source-image", "path": ""}, {"title": "bg-captcha-picture-red-point", "path": ""}, {"title": "bg-captcha-wrapper bg-captcha-wrapper-slider", "path": ""}, {"title": "bg-captcha-source", "path": ""}, {"title": "bg-captcha-source-image", "path": ""}, {"title": "bg-captcha-source-image-slider", "path": ""}, {"title": "bg-captcha-slider-handlebar", "path": ""}, {"title": "bg-captcha-slide-bg", "path": ""}, {"title": "bg-captcha-wrapper bg-captcha-wrapper-gesture", "path": ""}, {"title": "bg-captcha-gesture-txt", "path": ""}, {"title": "bg-captcha-source", "path": ""}, {"title": "bg-captcha-wrapper bg-captcha-wrapper-input", "path": ""}, {"title": "bg-captcha-source", "path": ""}, {"title": "bg-captcha-source-image", "path": ""}, {"title": "bg-captcha-smart-loading-icon", "path": ""}, {"title": "bg-captcha-smart-success-icon", "path": ""}, {"title": "bg-captcha-wrapper bg-captcha-wrapper-smart", "path": ""}, {"title": "bg-captcha-wrapper-smart-img", "path": ""}, {"title": "bg-captcha-wrapper-smart-txt", "path": ""}, {"title": "bg-captcha-wrapper-smart-btn", "path": ""}, {"title": "bg-captcha-desktop-modal-overlay", "path": ""}, {"title": "bg-captcha-desktop-modal-content", "path": ""}, {"title": "bg-captcha-desktop-modal-header", "path": ""}, {"title": "bg-captcha-desktop-modal-close", "path": ""}, {"title": "bg-captcha-desktop-modal-body", "path": ""}, {"title": "bg-captcha-desktop-loading", "path": ""}, {"title": "bg-captcha-desktop-loading-position", "path": ""}, {"title": "bg-captcha-desktop-loading-icon", "path": ""}, {"title": "bg-captcha-desktop-loading-txt", "path": ""}, {"title": "bg-captcha-desktop-verify", "path": ""}, {"title": "bg-captcha-desktop-verify-position", "path": ""}, {"title": "bg-captcha-desktop-success-icon", "path": ""}, {"title": "bg-captcha-desktop-success-txt", "path": ""}, {"title": "bg-captcha-desktop-warn-icon", "path": ""}, {"title": "bg-captcha-desktop-warn-txt", "path": ""}, {"title": "bg-captcha-desktop-refresh", "path": ""}, {"title": "bg-captcha-desktop-input-refresh", "path": ""}, {"title": "bg-captcha-desktop-toast-warn-icon", "path": ""}, {"title": "bg-captcha-desktop-toast-success-icon", "path": ""}, {"title": "bg-captcha-desktop-toast", "path": ""}, {"title": "bg-captcha-desktop-toast-base bg-captcha-desktop-toast-", "path": ""}, {"title": "bg-captcha-desktop-toast-message", "path": ""}, {"title": "bg-captcha-desktop-input-wrapper", "path": ""}, {"title": "bg-captcha-desktop-input", "path": ""}, {"title": "fetch-plugin-captcha", "path": ""}, {"title": "fetch-plugin-pmm", "path": ""}, {"title": "fetch-plugin-result-unify", "path": ""}, {"title": "fetch-plugin-risk", "path": ""}, {"title": "fetch-plugin-status", "path": ""}, {"title": "an object", "path": ""}, {"title": "isOptionalString", "path": ""}, {"title": "isOptionalNumber", "path": ""}, {"title": "isOptionalBoolean", "path": ""}, {"title": "string", "path": ""}, {"title": "string", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$rcNotificationFadeIn", "path": ""}, {"title": "$rcDialogFadeOut", "path": ""}, {"title": "beast-click-notice-", "path": ""}, {"title": "error-screen-wrapper", "path": ""}, {"title": "fail-title", "path": ""}, {"title": "pmm/offlineCMTStore", "path": ""}, {"title": "pmm-statistics", "path": ""}, {"title": "error-screen-wrapper", "path": ""}, {"title": "fail-title", "path": ""}, {"title": "authorization_wrap__0b0lf", "path": ""}, {"title": "authorization_wrapContent__aaNlX", "path": ""}, {"title": "authorization_title__Mv+lV", "path": ""}, {"title": "authorization_providerList__LUaPV", "path": ""}, {"title": "authorization_subTitle__dBjQ3", "path": ""}, {"title": "authorization_display__A44D-", "path": ""}, {"title": "authorization_displaySplit__d-wbR", "path": ""}, {"title": "authorization_contentWrap__p9Bim", "path": ""}, {"title": "authorization_descWrap__v-Clp", "path": ""}, {"title": "authorization_descTitle__I347y", "path": ""}, {"title": "myCarousel_carouselWrap__+sziP", "path": ""}, {"title": "myCarousel_carouselTitle__Lz6QU", "path": ""}, {"title": "myCarousel_carouselTitleImg__3S42e", "path": ""}, {"title": "myCarousel_carouselImg__yE+bP", "path": ""}, {"title": "myCarousel_carouselImgDesc__LwEbu", "path": ""}, {"title": "authorizeCWModal_susTitle__lFnJ+", "path": ""}, {"title": "authorizeCWModal_susSubTitle__BcK1C", "path": ""}, {"title": "authorizeCWModal_wrap__PgdLJ", "path": ""}, {"title": "authorizeCWModal_closeIcon__U4HOk", "path": ""}, {"title": "authorizeCWModal_content__GGy52", "path": ""}, {"title": "authorizeCWModal_footer__c9v6k", "path": ""}, {"title": "authorizeCWModal_confirmBtn__i3SQT", "path": ""}, {"title": "myCarousel_carouselWrap__hrT1w", "path": ""}, {"title": "myCarousel_carouselTitle__XXVHp", "path": ""}, {"title": "myCarousel_carouselTitleImg__mzsmd", "path": ""}, {"title": "myCarousel_carouselImg__LQE-+", "path": ""}, {"title": "myCarousel_carouselImgDesc__G+1fo", "path": ""}, {"title": "skuList_skuInfo__uRg+5", "path": ""}, {"title": "skuList_skuInfoImg__hZrKd", "path": ""}, {"title": "skuList_skuInfoTitle__7DwQr", "path": ""}, {"title": "skuList_wrap__pJukA", "path": ""}, {"title": "skuList_summary__XSPEE", "path": ""}, {"title": "bindGoodsAfterAuthorizedCWModal_wrap__6uc9Y", "path": ""}, {"title": "bindGoodsAfterAuthorizedCWModal_closeIcon__nDZGa", "path": ""}, {"title": "bindGoodsAfterAuthorizedCWModal_content__r3Lax", "path": ""}, {"title": "bindGoodsAfterAuthorizedCWModal_listWrap__WL38n", "path": ""}, {"title": "bindGoodsAfterAuthorizedCWModal_listTitle__4eSCW", "path": ""}, {"title": "bindGoodsAfterAuthorizedCWModal_listTitleImg__usGoD", "path": ""}, {"title": "bindGoodsAfterAuthorizedCWModal_footer__wQUZQ", "path": ""}, {"title": "bindGoodsAfterAuthorizedCWModal_confirmBtn__+HU+D", "path": ""}, {"title": "bindGoodsAfterAuthorizedCWModal_susTitle__jpDDl", "path": ""}, {"title": "bindGoodsAfterAuthorizedCWModal_susSubTitle__4BxTY", "path": ""}, {"title": "SKC", "path": ""}, {"title": "forceSwitchCWShipmentModal_wrap__tHRmh", "path": ""}, {"title": "forceSwitchCWShipmentModal_closeIcon__PPJQf", "path": ""}, {"title": "forceSwitchCWShipmentModal_title__Tcpp1", "path": ""}, {"title": "forceSwitchCWShipmentModal_tips__XISLz", "path": ""}, {"title": "forceSwitchCWShipmentModal_footer__Q9vbq", "path": ""}, {"title": "forceSwitchCWShipmentModal_alertContent__Jkkyd", "path": ""}, {"title": "forceSwitchCWShipmentModal_alertTitle__jqocA", "path": ""}, {"title": "forceSwitchCWShipmentModal_alertTipImg__OXCBG", "path": ""}, {"title": "panel_wrap__o2O1X", "path": ""}, {"title": "panel_title__ee8nz", "path": ""}, {"title": "panel_btn__j21LX", "path": ""}, {"title": "horizontalScroll_wrap__OKXhq", "path": ""}, {"title": "horizontalScroll_scrollContainer__XK6Dy", "path": ""}, {"title": "horizontalScroll_scrollContent__yApFd", "path": ""}, {"title": "horizontalScroll_bar__Sz0Ow", "path": ""}, {"title": "stepTitle_wrap__SQ5s4", "path": ""}, {"title": "stepTitle_num__v6WNc", "path": ""}, {"title": "stepTitle_text__N+BKR", "path": ""}, {"title": "desc_wrap__ia2v8", "path": ""}, {"title": "desc_title__vwYrj", "path": ""}, {"title": "desc_upload__a8n1i", "path": ""}, {"title": "desc_uploadSubText__CIoAr", "path": ""}, {"title": "myCarousel_carouselWrap__VDflH", "path": ""}, {"title": "myCarousel_carouselTitle__TwW+8", "path": ""}, {"title": "myCarousel_carouselTitleImg__osq9P", "path": ""}, {"title": "myCarousel_carouselImg__WoSzl", "path": ""}, {"title": "myCarousel_carouselImgDesc__mnzfM", "path": ""}, {"title": "transferCWModal_wrap__LKf2Q", "path": ""}, {"title": "transferCWModal_closeIcon__MKwjl", "path": ""}, {"title": "transferCWModal_title__XHimR", "path": ""}, {"title": "transferCWModal_subTitle__yf6-m", "path": ""}, {"title": "transferCWModal_content__-M8x5", "path": ""}, {"title": "transferCWModal_footer__3t2VB", "path": ""}, {"title": "warehousePublicizeModal_container__VvoXG", "path": ""}, {"title": "warehousePublicizeModal_img__MCiii", "path": ""}, {"title": "warehousePublicizeModal_icon__yNvmX", "path": ""}, {"title": "warehousePublicizeModal_btn__whOo7", "path": ""}, {"title": "ExtendDeliveryTimeModal_regionId1Name__y0wSD", "path": ""}, {"title": "ModifyCertWarehouseModal_wrap__DvTrU", "path": ""}, {"title": "ModifyCertWarehouseModal_title__r-Ji2", "path": ""}, {"title": "ModifyCertWarehouseModal_notice__yP2AW", "path": ""}, {"title": "ModifyCertWarehouseModal_tip__idu+0", "path": ""}, {"title": "ModifyCertWarehouseModal_guide__pdlga", "path": ""}, {"title": "ModifyCertWarehouseModal_opreate__a5j9F", "path": ""}, {"title": "OnlineShipSubsidyModalV2_container__Yjekc", "path": ""}, {"title": "OnlineShipSubsidyModalV2_bgImg__kPl4I", "path": ""}, {"title": "OnlineShipSubsidyModalV2_monthImg__wtapq", "path": ""}, {"title": "OnlineShipSubsidyModalV2_content__PrTC2", "path": ""}, {"title": "OnlineShipSubsidyModalV2_subsidyMoney__o3kzt", "path": ""}, {"title": "OnlineShipSubsidyModalV2_dollor__+bAXs", "path": ""}, {"title": "OnlineShipSubsidyModalV2_ratioDiffText__2-4x2", "path": ""}, {"title": "OnlineShipSubsidyModalV2_desc__w0TD3", "path": ""}, {"title": "OnlineShipSubsidyModalV2_detailBtn__Dq8vo", "path": ""}, {"title": "OnlineShipSubsidyModalV2_close__xrXgc", "path": ""}, {"title": "ShippingWarehousePromptModal_wrap__qe4s9", "path": ""}, {"title": "ShippingWarehousePromptModal_title__+jnQK", "path": ""}, {"title": "ShippingWarehousePromptModal_notice__muHT9", "path": ""}, {"title": "ShippingWarehousePromptModal_tip__kIvUy", "path": ""}, {"title": "ShippingWarehousePromptModal_guide__+o3dj", "path": ""}, {"title": "ShippingWarehousePromptModal_opreate__tnEmx", "path": ""}, {"title": "FreeShippingSignModal_footerWrap__nPba9", "path": ""}, {"title": "FreeShippingSignModal_footerTip__g7vlx", "path": ""}, {"title": "FreeShippingSignModal_policyLink__9Vi2x", "path": ""}, {"title": "FreeShippingSignModal_headerWrap__4zdJ1", "path": ""}, {"title": "FreeShippingSignModal_header__vmHXJ", "path": ""}, {"title": "FreeShippingSignModal_headerTitle__3AyIb", "path": ""}, {"title": "FreeShippingSignModal_headerTip__+Va4R", "path": ""}, {"title": "string", "path": ""}, {"title": "string", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$rcNotificationFadeIn", "path": ""}, {"title": "$rcDialogFadeOut", "path": ""}, {"title": "beast-click-notice-", "path": ""}, {"title": "error-screen-wrapper", "path": ""}, {"title": "fail-title", "path": ""}, {"title": "error-screen-wrapper", "path": ""}, {"title": "fail-title", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "string", "path": ""}, {"title": "string", "path": ""}, {"title": "error-screen-wrapper", "path": ""}, {"title": "fail-title", "path": ""}, {"title": "$rcNotificationFadeIn", "path": ""}, {"title": "$rcDialogFadeOut", "path": ""}, {"title": "beast-click-notice-", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "string", "path": ""}, {"title": "string", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "picture", "path": ""}, {"title": "index-module__modal_crop_size___2KBY3", "path": ""}, {"title": "index-module__cropper_contain___KEGsW", "path": ""}, {"title": "index-module__cropper_btn___1oN06", "path": ""}, {"title": "index-module__crop_img_area___z-H7W", "path": ""}, {"title": "index-module__memory_img_list___23DiA", "path": ""}, {"title": "index-module__base_img_wrap___3JmCs", "path": ""}, {"title": "index-module__selected_wrap___JcLbF", "path": ""}, {"title": "index-module__img_selected___3TVGT", "path": ""}, {"title": "index-module__img_check___3GL2y", "path": ""}, {"title": "index-module__crop_img_empty___3quev", "path": ""}, {"title": "index-module__waiting_upload_area___vsuzO", "path": ""}, {"title": "index-module__wait_img_list___3RkCB", "path": ""}, {"title": "index-module__wait_img_wrap___etfJg", "path": ""}, {"title": "index-module__wait_img_del___2ZkOU", "path": ""}, {"title": "index-module__crop_modal_contain___3Bw-l", "path": ""}, {"title": "error-screen-wrapper", "path": ""}, {"title": "fail-title", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "string", "path": ""}, {"title": "string", "path": ""}, {"title": "$rcNotificationFadeIn", "path": ""}, {"title": "$rcDialogFadeOut", "path": ""}, {"title": "beast-click-notice-", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "label-value-module__label-value___1aG2L", "path": ""}, {"title": "index-module__menuName___1mJjg", "path": ""}, {"title": "index-module__name___3iom5", "path": ""}, {"title": "index-module__badgeWrapper___19NKT", "path": ""}, {"title": "index-module__new1___hXtZM", "path": ""}, {"title": "index-module__newText___2tS8e", "path": ""}, {"title": "index-module__newHintDot___2q91Y", "path": ""}, {"title": "index-module__newHintText___1BShy", "path": ""}, {"title": "index-module__badgeWrapper___10MHz", "path": ""}, {"title": "index-module__new___qC-8I", "path": ""}, {"title": "index-module__newText___1A1ry", "path": ""}, {"title": "index-module__newHintDot___1C9hR", "path": ""}, {"title": "index-module__newHintText___3P3ol", "path": ""}, {"title": "index-module__wrapper___1KSFL", "path": ""}, {"title": "index-module__wrapper___1LqNK", "path": ""}, {"title": "index-module__wrapper___ZkVQT", "path": ""}, {"title": "index-module__hint___31H7-", "path": ""}, {"title": "index-module__wrapper___J340r", "path": ""}, {"title": "index-module__tabWrapper___3D3TX", "path": ""}, {"title": "index-module__hintWrapper___QsfOf", "path": ""}, {"title": "error-screen-wrapper", "path": ""}, {"title": "fail-title", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleIn", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut, $scaleOut", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$fadeIn, $scaleYIn", "path": ""}, {"title": "$fadeOut, $scaleYOut", "path": ""}, {"title": "$rcNotificationFadeIn", "path": ""}, {"title": "$rcDialogFadeOut", "path": ""}, {"title": "beast-click-notice-", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "$fadeIn", "path": ""}, {"title": "$fadeOut", "path": ""}, {"title": "picture", "path": ""}, {"title": "index-module__errorWrapper___1ypOf", "path": ""}, {"title": "index-module__text___3ZV0O", "path": ""}, {"title": "localforage", "path": ""}, {"title": "keyvaluepairs", "path": ""}, {"title": "wrapper", "path": ""}, {"title": "紀元前", "path": ""}, {"title": "<PERSON><PERSON>", "path": ""}, {"title": "Before Christ", "path": ""}, {"title": "+String(t.name)),e.respectFunctionProperties&&this._object(t)},_number:function(e){return n(", "path": ""}, {"title": "react-dom", "path": ""}, {"title": "Calib<PERSON>", "path": ""}, {"title": "Normal", "path": ""}, {"title": "SH33TJSNAME", "path": ""}, {"title": "SH33TJSERRY", "path": ""}, {"title": "_xlnm._FilterDatabase", "path": ""}, {"title": "_xlnm._FilterDatabase", "path": ""}, {"title": "ThisWorkbook", "path": ""}, {"title": "<PERSON><PERSON>", "path": ""}, {"title": "+e);if(n){var a=r&&r[o]&&r[o].CodeName||e;if(95==a.charCodeAt(0)&&a.length>22)throw new Error(", "path": ""}, {"title": ").concat((0,s.qQ)(o),", "path": ""}, {"title": "@afe/pmm-sdk", "path": ""}, {"title": "temu-sca-container", "path": ""}, {"title": "<PERSON><PERSON> Central", "path": ""}, {"title": "modal-content_acceptTitle__rUEEV", "path": ""}, {"title": "modal-content_example1ContentTitle__HdmcU", "path": ""}, {"title": "invite-coupon_headerTitle__IEoJ4", "path": ""}, {"title": "invite-coupon_couponContentTitle__mKGhX", "path": ""}, {"title": "SPU", "path": ""}, {"title": "SKC ID", "path": ""}, {"title": "SPU ID", "path": ""}, {"title": "SKC/SKU ID", "path": ""}, {"title": "SPU", "path": ""}, {"title": "rule-item_title__wRei7", "path": ""}, {"title": "account-info_title__35m9u", "path": ""}, {"title": "batch-setting_label__yB60C", "path": ""}, {"title": "SPU：", "path": ""}, {"title": "SKC：", "path": ""}, {"title": "SKU：", "path": ""}, {"title": "not-same-reason-modal_container__dT10S", "path": ""}, {"title": "not-same-reason-modal_titleWrap__gkC-6", "path": ""}, {"title": "not-same-reason-modal_desc__JIQ8f", "path": ""}, {"title": "not-same-reason-modal_formWrap__3aZRB", "path": ""}, {"title": "not-same-reason-modal_footer__-V6cK", "path": ""}, {"title": "bgb-sca-main", "path": ""}, {"title": "bgb-sca-settlement", "path": ""}, {"title": "bg-merchant-chat", "path": ""}, {"title": "bg-agent-seller-lgst", "path": ""}, {"title": "agentseller-open-web", "path": ""}, {"title": "agentseller-govern-web", "path": ""}, {"title": "temu-sc-open", "path": ""}, {"title": "temu-sc-wms", "path": ""}, {"title": "temu-sca-stock", "path": ""}, {"title": "temu-sca-material", "path": ""}, {"title": "temu-sca-sample", "path": ""}, {"title": "temu-sc-activity", "path": ""}, {"title": "temu-sca-goods", "path": ""}, {"title": "temu-sca-newon", "path": ""}, {"title": "activity-banner_banner__xo6RR", "path": ""}, {"title": "activity-banner_top__9yHbU", "path": ""}, {"title": "loading-card_container__GU92P", "path": ""}, {"title": "multiple-mall-merge_main__YTIJJ", "path": ""}, {"title": "multiple-mall-merge_leftCnt__fg+33", "path": ""}, {"title": "multiple-mall-merge_rightCnt__-Pptq", "path": ""}, {"title": "multiple-mall-merge_mallSelectCnt__7KiOW", "path": ""}, {"title": "failed-page_container__+WdLm", "path": ""}, {"title": "failed-page_img__KyMCK", "path": ""}, {"title": "failed-page_tips__hcilO", "path": ""}, {"title": "modal-content_platBanner__ehdl7", "path": ""}, {"title": "modal-content_example__y6dJL", "path": ""}, {"title": "modal-content_actDetail__xJiVT", "path": ""}, {"title": "modal-content_downPriceBanner__kxz1b", "path": ""}, {"title": "modal-content_example2__GMVsN", "path": ""}, {"title": "modal-content_example3__8c92g", "path": ""}, {"title": "modal-content_actDetail2__ZqYXU", "path": ""}, {"title": "modal-content_acceptTitle__4XkVy", "path": ""}, {"title": "modal-content_closeTitle__ErDx6", "path": ""}, {"title": "modal-content_content__muW47", "path": ""}, {"title": "modal-content_header__QWcei", "path": ""}, {"title": "modal-content_headerLeft__N8XBp", "path": ""}, {"title": "modal-content_headerRight__8pbbV", "path": ""}, {"title": "modal-content_actInfo__uBLMx", "path": ""}, {"title": "modal-content_footer__cDZL1", "path": ""}, {"title": "modal-content_actRuleDesc__4bLCn", "path": ""}, {"title": "modal-content_important__tE1rj", "path": ""}, {"title": "modal-content_platBanner__sgU04", "path": ""}, {"title": "modal-content_example__XH4+N", "path": ""}, {"title": "modal-content_actDetail__u9XMb", "path": ""}, {"title": "modal-content_privilegeBanner__166RE", "path": ""}, {"title": "modal-content_privilegeActDetail__rowFr", "path": ""}, {"title": "modal-content_privilegeTitle__HZDdj", "path": ""}, {"title": "modal-content_explosiveTitle__GvPyf", "path": ""}, {"title": "modal-content_privilegeDesc__JviCC", "path": ""}, {"title": "modal-content_privilegePopContent__iN9rM", "path": ""}, {"title": "modal-content_closeTitle__j1uMu", "path": ""}, {"title": "modal-content_content__mSLsL", "path": ""}, {"title": "modal-content_header__lt12T", "path": ""}, {"title": "modal-content_headerLeft__ScwlX", "path": ""}, {"title": "modal-content_headerRight__nz6Xs", "path": ""}, {"title": "modal-content_actInfo__uuy7J", "path": ""}, {"title": "modal-content_footer__ew1Dx", "path": ""}, {"title": "modal-content_footerLeft__mp-mk", "path": ""}, {"title": "modal-content_actRuleDesc__Rkoma", "path": ""}, {"title": "modal-content_important__tnzOS", "path": ""}, {"title": "modal-content_downPriceBanner__LuzDP", "path": ""}, {"title": "modal-content_example1Content__UBmh4", "path": ""}, {"title": "modal-content_example1ContentTitle__lz3qH", "path": ""}, {"title": "modal-content_example2__YOsyK", "path": ""}, {"title": "modal-content_actDetail2__GGGZn", "path": ""}, {"title": "modal-content_acceptTitle__Cinci", "path": ""}, {"title": "modal-content_closeIcon__PCZl3", "path": ""}, {"title": "modal-content_content__F11zZ", "path": ""}, {"title": "modal-content_header__Gw9bc", "path": ""}, {"title": "modal-content_headerLeft__ZUbd1", "path": ""}, {"title": "modal-content_headerRight__qIakH", "path": ""}, {"title": "modal-content_actInfo__nH-fR", "path": ""}, {"title": "modal-content_footer__O4upr", "path": ""}, {"title": "modal-content_footerLeft__3Ns64", "path": ""}, {"title": "modal-content_actRuleDesc__t3Pid", "path": ""}, {"title": "modal-content_important__S0iYX", "path": ""}, {"title": "ad-guide-modal_signModal__KfUUD", "path": ""}, {"title": "ad-guide-modal_close__Hh4ns", "path": ""}, {"title": "ad-guide-modal_footer__XZE57", "path": ""}, {"title": "copy-text_copy__8jieA", "path": ""}, {"title": "exam-log_content__xiKBy", "path": ""}, {"title": "exam-log_examResult__-y7gO", "path": ""}, {"title": "exam-log_mainSection__fSLRm", "path": ""}, {"title": "exam-log_examTitle__899BE", "path": ""}, {"title": "exam-log_questionList__le5ed", "path": ""}, {"title": "exam-log_questionSection__hLwz+", "path": ""}, {"title": "exam-log_passImg__JOOnS", "path": ""}, {"title": "exam-log_questionTitle__ztujc", "path": ""}, {"title": "exam-log_questionType__faFw0", "path": ""}, {"title": "exam-log_explain__tKOMA", "path": ""}, {"title": "exam-detail_preparationExam__R9OR8", "path": ""}, {"title": "exam-detail_container__xgOUZ", "path": ""}, {"title": "exam-detail_mainSection__YbtcD", "path": ""}, {"title": "exam-detail_examTitle__yh5sw", "path": ""}, {"title": "exam-detail_needInstruction__di6tz", "path": ""}, {"title": "exam-detail_preExamInstruction__Hn3dl", "path": ""}, {"title": "exam-detail_questionSection__iv85U", "path": ""}, {"title": "exam-detail_num__tAZ4l", "path": ""}, {"title": "exam-detail_questionTitle__iaED3", "path": ""}, {"title": "exam-detail_explain__PuK6e", "path": ""}, {"title": "exam-list_container__d18pd", "path": ""}, {"title": "exam-list_title__vbhEZ", "path": ""}, {"title": "exam-list_examItem__s-TpH", "path": ""}, {"title": "exam-list_examInfo__vVDnp", "path": ""}, {"title": "exam-list_infoContainer__4sxLL", "path": ""}, {"title": "exam-list_examName__GoIC8", "path": ""}, {"title": "exam-list_requireInfo__XI--s", "path": ""}, {"title": "exam-list_beforeShouldKnow__i4ps5", "path": ""}, {"title": "exam-list_content__d-htr", "path": ""}, {"title": "components_container__-i-Z+", "path": ""}, {"title": "components_title__-RQHw", "path": ""}, {"title": "components_success__u7dXE", "path": ""}, {"title": "components_footer__U5QeD", "path": ""}, {"title": "modal_categoryName__Vloj+", "path": ""}, {"title": "recommend-coupon-item_recCouponItem__Zqak2", "path": ""}, {"title": "recommend-coupon-item_recCouponItemLeft__1XzNl", "path": ""}, {"title": "recommend-coupon-item_recCouponItemRight__kdFOW", "path": ""}, {"title": "recommend-coupon-item_recCouponItemRightText__D7d7Z", "path": ""}, {"title": "recommend-coupon-item_price__U0fdM", "path": ""}, {"title": "limit-flow-adjust-price_dividerStr__JIHqT", "path": ""}, {"title": "limit-flow-adjust-price_priceBox__E4Kz0", "path": ""}, {"title": "limit-flow-adjust-price_normalPriceBox__itf4y", "path": ""}, {"title": "limit-flow-adjust-price_activityPriceBox__DbQZd", "path": ""}, {"title": "one-key-open-semi-mall-modal_successModal__i061U", "path": ""}, {"title": "one-key-open-semi-mall-modal_tip__r40PU", "path": ""}, {"title": "one-key-open-semi-mall-modal_signModalY2__Z5MLt", "path": ""}, {"title": "one-key-open-semi-mall-modal_signModal__lffZW", "path": ""}, {"title": "one-key-open-semi-mall-modal_codeInfo__0p5vR", "path": ""}, {"title": "components_lightingColor__sAMrb", "path": ""}, {"title": "components_operator__-zCQs", "path": ""}, {"title": "components_warnHeader__J2hEO", "path": ""}, {"title": "components_warnContent__PutES", "path": ""}, {"title": "components_redDown__2209S", "path": ""}, {"title": "components_lightingColor__lKZsa", "path": ""}, {"title": "components_operator__gCvVZ", "path": ""}, {"title": "components_warnHeader__13wP-", "path": ""}, {"title": "components_redColor__uZ9lb", "path": ""}, {"title": "grape-price-adjust-confirm_customBanner__Um73g", "path": ""}, {"title": "grape-price-adjust-confirm_banner__Jn0Lr", "path": ""}, {"title": "grape-price-adjust-confirm_contentWrp__Kcybg", "path": ""}, {"title": "grape-price-adjust-confirm_content__EZTMS", "path": ""}, {"title": "grape-price-adjust-confirm_header__gWSzT", "path": ""}, {"title": "grape-price-adjust-confirm_notice__ShCBa", "path": ""}, {"title": "grape-price-adjust-confirm_updated__Z8pxl", "path": ""}, {"title": "components_lightingColor__QwDcI", "path": ""}, {"title": "components_operator__XTPC9", "path": ""}, {"title": "components_warnHeader__jZdK0", "path": ""}, {"title": "components_warnContent__S+PF2", "path": ""}, {"title": "components_redColor__ALTb3", "path": ""}, {"title": "market-price-adjust-confirm_banner__UZV1d", "path": ""}, {"title": "market-price-adjust-confirm_topTip__+en12", "path": ""}, {"title": "market-price-adjust-confirm_content__Vp65Y", "path": ""}, {"title": "market-price-adjust-confirm_header__OIMuz", "path": ""}, {"title": "market-price-adjust-confirm_updated__rI6S7", "path": ""}, {"title": "components_lightingColor__ZFvEB", "path": ""}, {"title": "components_operator__Nyz7z", "path": ""}, {"title": "components_warnHeader__2vPD9", "path": ""}, {"title": "components_warnContent__hRwt3", "path": ""}, {"title": "components_redColor__Olie0", "path": ""}, {"title": "new-price-adjust-confirm_banner__LGdJA", "path": ""}, {"title": "new-price-adjust-confirm_content__JCUM8", "path": ""}, {"title": "new-price-adjust-confirm_header__vCzeR", "path": ""}, {"title": "new-price-adjust-confirm_notice__li7CD", "path": ""}, {"title": "new-price-adjust-confirm_updated__bezD-", "path": ""}, {"title": "components_lightingColor__V4eHw", "path": ""}, {"title": "components_operator__sfWlF", "path": ""}, {"title": "components_warnHeader__0m99D", "path": ""}, {"title": "components_warnContent__VbEyz", "path": ""}, {"title": "components_redColor__03fyD", "path": ""}, {"title": "old-price-adjust-confirm_banner__ecgjv", "path": ""}, {"title": "old-price-adjust-confirm_content__fYYLH", "path": ""}, {"title": "old-price-adjust-confirm_title__8Lf7h", "path": ""}, {"title": "old-price-adjust-confirm_iptText__iDwfC", "path": ""}, {"title": "old-price-adjust-confirm_notice__7tUsl", "path": ""}, {"title": "old-price-adjust-confirm_tips__j3io-", "path": ""}, {"title": "old-price-adjust-confirm_updated__R25FF", "path": ""}, {"title": "product-migrate-modal_container__dLKlf", "path": ""}, {"title": "product-migrate-modal_footer__1E3D3", "path": ""}, {"title": "delivery-mode-render_container__ZrZjQ", "path": ""}, {"title": "delivery-mode-render_modeText__UjD1O", "path": ""}, {"title": "copy-text_copy__9cdNx", "path": ""}, {"title": "hot-tag_tag__7-uFD", "path": ""}, {"title": "hot-tag_img__5XA0c", "path": ""}, {"title": "product-info_infoContainer__KD9nh", "path": ""}, {"title": "product-info_productName__SyG3d", "path": ""}, {"title": "stock-input_radioBlock__zk6cN", "path": ""}, {"title": "stock-input_radioItem__KxLxz", "path": ""}, {"title": "stock-input_rightBlock__Cvlil", "path": ""}, {"title": "stock-input_hitEuLimitStockEditTips__d1g6N", "path": ""}, {"title": "footer-block_container__iBHlg", "path": ""}, {"title": "error-table-content_container__T9EOd", "path": ""}, {"title": "add-stock-modal_submitStockTotal__+j5Zz", "path": ""}, {"title": "add-stock-modal_container__pHY8r", "path": ""}, {"title": "add-stock-modal_title__DsQjA", "path": ""}, {"title": "add-stock-modal_headerTips__U1cQf", "path": ""}, {"title": "add-stock-modal_mainTips__5jgqy", "path": ""}, {"title": "add-stock-modal_numProduct__V5Hz+", "path": ""}, {"title": "add-stock-modal_subTips__CZAIm", "path": ""}, {"title": "add-stock-modal_hotNum__7c3Nm", "path": ""}, {"title": "add-stock-modal_settingBlock__iUFef", "path": ""}, {"title": "add-stock-modal_failedCount__HSIKd", "path": ""}, {"title": "simliar-info_container__2+RLq", "path": ""}, {"title": "simliar-info_subTitle__OCDDp", "path": ""}, {"title": "simliar-info_feedback__Uvh7w", "path": ""}, {"title": "failed-page_container__hREAI", "path": ""}, {"title": "failed-page_img__72hmb", "path": ""}, {"title": "failed-page_tips__D57SN", "path": ""}, {"title": "sale-forbid-tag_display__JrUPu", "path": ""}, {"title": "sale-forbid-tag_skuContainer__W3Heg", "path": ""}, {"title": "sale-forbid-tag_img__RiKDg", "path": ""}, {"title": "sale-forbid-tag_skuInfo__Np3Bj", "path": ""}, {"title": "sale-forbid-tag_skuTag__N0doN", "path": ""}, {"title": "sale-forbid-tag_compete__EOnZJ", "path": ""}, {"title": "sale-forbid-tag_competeTag__tuiGF", "path": ""}, {"title": "components_lightingColor__KU6pi", "path": ""}, {"title": "components_operator__EODWc", "path": ""}, {"title": "components_abnormalHeader__Eb6fX", "path": ""}, {"title": "components_icon__FDAHq", "path": ""}, {"title": "components_impRed__dboUt", "path": ""}, {"title": "components_tips__N-Vw9", "path": ""}, {"title": "components_warnHeader__Dyfj6", "path": ""}, {"title": "components_warnContent__aULEX", "path": ""}, {"title": "components_redColor__hCm-C", "path": ""}, {"title": "semi-price-adjust-confirm_orangeColor__MnLVY", "path": ""}, {"title": "semi-price-adjust-confirm_bg__ozUQr", "path": ""}, {"title": "semi-price-adjust-confirm_header__MFNVW", "path": ""}, {"title": "semi-price-adjust-confirm_iptText__KhfFB", "path": ""}, {"title": "semi-price-adjust-confirm_tableContent__0Eotf", "path": ""}, {"title": "semi-price-adjust-confirm_notice__zxz3x", "path": ""}, {"title": "semi-price-adjust-confirm_updated__9pR5f", "path": ""}, {"title": "failed-page_container__<PERSON>ios", "path": ""}, {"title": "failed-page_img__IbSRx", "path": ""}, {"title": "failed-page_tips__wbyGB", "path": ""}, {"title": "sale-forbid-tag_display__KZbsI", "path": ""}, {"title": "sale-forbid-tag_skuContainer__pwyTW", "path": ""}, {"title": "sale-forbid-tag_img__MHs-t", "path": ""}, {"title": "sale-forbid-tag_skuInfo__Iy-6X", "path": ""}, {"title": "sale-forbid-tag_skuTag__i99bQ", "path": ""}, {"title": "sale-forbid-tag_compete__ofMQt", "path": ""}, {"title": "sale-forbid-tag_competeTag__+F7l4", "path": ""}, {"title": "components_lightingColor__svH6J", "path": ""}, {"title": "components_operator__MBqjA", "path": ""}, {"title": "components_abnormalHeader__bfKAl", "path": ""}, {"title": "components_icon__KUUKV", "path": ""}, {"title": "components_impRed__AZxK2", "path": ""}, {"title": "components_tips__UdVnI", "path": ""}, {"title": "components_warnHeader__EkcWG", "path": ""}, {"title": "components_warnContent__-GtxF", "path": ""}, {"title": "components_redColor__x9E0j", "path": ""}, {"title": "semi-price-adjust-confirm-old_bg__srZB3", "path": ""}, {"title": "semi-price-adjust-confirm-old_title__NefOZ", "path": ""}, {"title": "semi-price-adjust-confirm-old_header__9bMp6", "path": ""}, {"title": "semi-price-adjust-confirm-old_iptText__43peT", "path": ""}, {"title": "semi-price-adjust-confirm-old_notice__gslka", "path": ""}, {"title": "semi-price-adjust-confirm-old_tips__qybFa", "path": ""}, {"title": "semi-price-adjust-confirm-old_updated__7Rv-B", "path": ""}, {"title": "goods-card_card__us64h", "path": ""}, {"title": "goods-card_info__oye8m", "path": ""}, {"title": "goods-card_title__+HitJ", "path": ""}, {"title": "goods-card_button__cquwR", "path": ""}, {"title": "unsold-stock-confirm-modal_time__F79Dc", "path": ""}, {"title": "unsold-stock-confirm-modal_modalInner__y-Iwy", "path": ""}, {"title": "unsold-stock-confirm-modal_modalInnerContent__4L8tZ", "path": ""}, {"title": "unsold-stock-confirm-modal_modalInnerTitleBg__tNvLA", "path": ""}, {"title": "advice-transform-to-jit-modal_wrapper__cSAd5", "path": ""}, {"title": "advice-transform-to-jit-modal_close__dLzsG", "path": ""}, {"title": "advice-transform-to-jit-modal_headerWrapper__vLIg1", "path": ""}, {"title": "advice-transform-to-jit-modal_titleHave__+P79F", "path": ""}, {"title": "advice-transform-to-jit-modal_number__YzvqY", "path": ""}, {"title": "advice-transform-to-jit-modal_account__3ayCw", "path": ""}, {"title": "advice-transform-to-jit-modal_content__LiCUE", "path": ""}, {"title": "advice-transform-to-jit-modal_footer__nsV+o", "path": ""}, {"title": "advice-transform-to-jit-modal_selectedText__ZvmWa", "path": ""}, {"title": "advice-transform-to-jit-modal_selectedTextCount__tLgDr", "path": ""}, {"title": "advice-transform-to-jit-modal_footerText__B8+VD", "path": ""}, {"title": "advice-transform-to-jit-modal_withFooter<PERSON><PERSON>per__nITea", "path": ""}, {"title": "delay-exemption-modal_signModal__IgE7O", "path": ""}, {"title": "delay-exemption-modal_close__bVosD", "path": ""}, {"title": "delay-exemption-modal_footer__9kd4m", "path": ""}, {"title": "promotion-glo-guide-modal_japanSignModal__q-0KC", "path": ""}, {"title": "promotion-glo-guide-modal_australiaSignModal__3T4Mo", "path": ""}, {"title": "promotion-eu-guide-modal_saSignModal__gjMBt", "path": ""}, {"title": "promotion-eu-guide-modal_close__6eJBJ", "path": ""}, {"title": "promotion-eu-guide-modal_footer__a-L7Q", "path": ""}, {"title": "top-sale-same-publish-modal_container__Q3Ww7", "path": ""}, {"title": "top-sale-same-publish-modal_content__UXSfd", "path": ""}, {"title": "shipping-receipt-modal_wrapper__8OiXD", "path": ""}, {"title": "shipping-receipt-modal_close__BJwAy", "path": ""}, {"title": "flow-grow-adjust-price-modal_customBanner__DjbvY", "path": ""}, {"title": "flow-grow-adjust-price-modal_countText__r-TLF", "path": ""}, {"title": "flow-grow-adjust-price-modal_contentWrp__iRt6R", "path": ""}, {"title": "flow-grow-adjust-price-modal_content__da7IN", "path": ""}, {"title": "flow-grow-adjust-price-modal_tips__I-rcX", "path": ""}, {"title": "mideast-modal_close__BpFxq", "path": ""}, {"title": "mideast-modal_detailBtn__dd2vb", "path": ""}, {"title": "full-chance-invitation-modal_customBanner__YBSx5", "path": ""}, {"title": "full-chance-invitation-modal_contentWrp__oLK2x", "path": ""}, {"title": "full-chance-invitation-modal_content__KMlse", "path": ""}, {"title": "confirmed-price-adjust-plus_bg__rBwHP", "path": ""}, {"title": "confirmed-price-adjust-plus_container__GNTVA", "path": ""}, {"title": "confirmed-price-adjust-plus_title__Ufaos", "path": ""}, {"title": "confirmed-price-adjust-plus_tips__I1zHl", "path": ""}, {"title": "semi-confirmed-price-adjust-plus_bg__<PERSON><PERSON><PERSON><PERSON>", "path": ""}, {"title": "semi-confirmed-price-adjust-plus_container__p9R0-", "path": ""}, {"title": "semi-confirmed-price-adjust-plus_title__afZkB", "path": ""}, {"title": "semi-confirmed-price-adjust-plus_tips__fdgv7", "path": ""}, {"title": "gray-price-adjust-confirm-semi_modalBody__gArI7", "path": ""}, {"title": "gray-price-adjust-confirm-semi_content__ZxhZP", "path": ""}, {"title": "gray-price-adjust-confirm-semi_header__Efo8i", "path": ""}, {"title": "gray-price-adjust-confirm-semi_tips__xB5Ew", "path": ""}, {"title": "gray-price-adjust-confirm-semi_updated__rBP1R", "path": ""}, {"title": "price-adjust-confirm-system_customBanner__ld-vu", "path": ""}, {"title": "price-adjust-confirm-system_contentWrp__gWuTX", "path": ""}, {"title": "price-adjust-confirm-system_content__roP1W", "path": ""}, {"title": "price-adjust-confirm-system_header__D5hzP", "path": ""}, {"title": "price-adjust-confirm-system_sales__+n9ru", "path": ""}, {"title": "price-adjust-confirm-system_notice__3nQA3", "path": ""}, {"title": "price-adjust-confirm-system_updated__xCwuX", "path": ""}, {"title": "limit-flow-adjust-price_footer__DJrqv", "path": ""}, {"title": "limit-flow-adjust-price_submitTips__HJbo1", "path": ""}, {"title": "limit-flow-adjust-price_red__a64H3", "path": ""}, {"title": "limit-flow-adjust-price_orange__VW7i0", "path": ""}, {"title": "limit-flow-adjust-price_content__JZI26", "path": ""}, {"title": "limit-flow-adjust-price_divider__YOEzv", "path": ""}, {"title": "limit-flow-adjust-price_linkBtn__H5dh7", "path": ""}, {"title": "semi-limit-flow-adjust-price_footer__lZZYo", "path": ""}, {"title": "semi-limit-flow-adjust-price_pagination__yYh0S", "path": ""}, {"title": "semi-limit-flow-adjust-price_footerBtn__-XUOq", "path": ""}, {"title": "semi-limit-flow-adjust-price_submitTips__eTs+5", "path": ""}, {"title": "semi-limit-flow-adjust-price_red__loahs", "path": ""}, {"title": "semi-limit-flow-adjust-price_orange__qHPPQ", "path": ""}, {"title": "semi-limit-flow-adjust-price_content__yM3Gg", "path": ""}, {"title": "semi-limit-flow-adjust-price_divider__5BQuw", "path": ""}, {"title": "semi-limit-flow-adjust-price_tips__9SOxU", "path": ""}, {"title": "semi-limit-flow-adjust-price_linkBtn__sFwYx", "path": ""}, {"title": "satisfaction-score_list__4bCw-", "path": ""}, {"title": "satisfaction-score_title__sye6T", "path": ""}, {"title": "satisfaction-score_subtitle__-aLlY", "path": ""}, {"title": "satisfaction-score_footer__jzrWo", "path": ""}, {"title": "compliance-register-modal_signModal__vj3+F", "path": ""}, {"title": "compliance-register-modal_close__AxZkz", "path": ""}, {"title": "main-account-modal_signModal__VtojE", "path": ""}, {"title": "main-account-modal_close__NiImH", "path": ""}, {"title": "main-account-modal_title__bu3tq", "path": ""}, {"title": "main-account-modal_section__MO0ls", "path": ""}, {"title": "main-account-modal_desc__aQJgt", "path": ""}, {"title": "main-account-modal_footer__6q-xJ", "path": ""}, {"title": "sub-account-modal_signModal__TIK50", "path": ""}, {"title": "sub-account-modal_close__MbmZk", "path": ""}, {"title": "sub-account-modal_title__QWCj3", "path": ""}, {"title": "sub-account-modal_section__x09Xs", "path": ""}, {"title": "sub-account-modal_desc__+PS4C", "path": ""}, {"title": "sub-account-modal_footer__E5a5b", "path": ""}, {"title": "annual-confirm-modal_signModal__99IpF", "path": ""}, {"title": "annual-confirm-modal_close__++tii", "path": ""}, {"title": "guide-error-modal_signModal__DVuWV", "path": ""}, {"title": "guide-error-modal_close__usYdF", "path": ""}, {"title": "header-icon_commonIcon__JwVxL", "path": ""}, {"title": "header-icon_title__+lZt0", "path": ""}, {"title": "new-bell_messageItem__R<PERSON>uzr", "path": ""}, {"title": "new-bell_messageTitle__j7RCz", "path": ""}, {"title": "new-bell_messageFooter__MHuRQ", "path": ""}, {"title": "new-bell_right__dA5Pg", "path": ""}, {"title": "new-bell_container__eWEgQ", "path": ""}, {"title": "new-bell_header__sABhU", "path": ""}, {"title": "new-bell_content__OkpOK", "path": ""}, {"title": "new-bell_list__DhjdS", "path": ""}, {"title": "rule-center-icon_commonIcon__UjO9p", "path": ""}, {"title": "rule-center_container__F33sH", "path": ""}, {"title": "rule-center_header__OOrDl", "path": ""}, {"title": "rule-center_content__IACTQ", "path": ""}, {"title": "rule-center_list__uJqMN", "path": ""}, {"title": "temu-c-entry_main__Q3So7", "path": ""}, {"title": "account-info_mallName__7Mk2U", "path": ""}, {"title": "account-info_entityName__kct3g", "path": ""}, {"title": "show-change-account_signModal__1gMKQ", "path": ""}, {"title": "show-change-account_title__amDbB", "path": ""}, {"title": "show-change-account_loginTag__Ed0Gw", "path": ""}, {"title": "show-change-account_changeTag__ULb+c", "path": ""}, {"title": "show-change-account_footer__FGaA0", "path": ""}, {"title": "show-change-account_noChange__iB90p", "path": ""}, {"title": "layout-main_pageContainer__2sTbK", "path": ""}, {"title": "layout-main_systemInfo__p7YCc", "path": ""}, {"title": "layout-main_logo__4-uLE", "path": ""}, {"title": "layout-main_tag__5VdYk", "path": ""}, {"title": "layout-main_notice__utYPn", "path": ""}, {"title": "empty-placeholder_wrapper__FT31z", "path": ""}, {"title": "empty-placeholder_title__oZjQa", "path": ""}, {"title": "goods-info_goodsName__-O+Gv", "path": ""}, {"title": "go-agentseller-account_signModal__qBwU-", "path": ""}, {"title": "go-agentseller-account_title__w98N9", "path": ""}, {"title": "go-agentseller-account_content__kvQEt", "path": ""}, {"title": "go-agentseller-account_desc__Ag65H", "path": ""}, {"title": "normal-text-container_appendixTitle__PQm36", "path": ""}, {"title": "normal-text-container_appendix__AInI1", "path": ""}, {"title": "normal-text-container_img__WkGuG", "path": ""}, {"title": "fetch-plugin-result-clean", "path": ""}], "components": ["GuideFilePage", "QuickLastPage", "ImageView", "BlankScreen", "WebView", "ToView", "OrderDifferentInView", "TurnPage", "UseView", "BrtBeginCsView", "PricePopupNextPage", "OnSinglePage", "ListByPage", "HRefScreen", "InView", "PageInfoView", "BrtEndUserShView", "EUPopupView", "BrtPage", "NewView", "BrtEndWsView", "BrtBeginUserCsView", "HomePage", "GoodsMmsPage", "BrtEndSXView", "BrtEndUserCsView", "BrtBeginSXView", "VerticalPage", "ReducePricePopupNextPage", "CodePage", "BrtCsPage", "BrtEndCsView", "BrtEndPCDSCPage", "OffScreen", "SkcPage", "ComponentView", "IntoView", "TemuPage", "BrtUserBookView", "BrtBeginPCDSCPage", "WBView", "SXView", "HorizontalPage", "BrtRRUserView", "BrtBeginBookView", "User<PERSON>iew", "WorkbookView", "PlaceholderInView", "SxView", "ChartView", "AboveView", "InHomePage", "BeforePage", "DataView", "BrtEndBookView", "BrtBeginWsView", "UserBView", "FloatPage", "FullScreen", "BrtBookView", "RRDUserView", "BrtBeginUserShView", "FilePage"], "accessible_routes": [{"route": "//", "url": "https://agentseller.temu.com//", "status_code": 200}, {"route": "/01/01", "url": "https://agentseller.temu.com/01/01", "status_code": 200}, {"route": "/1", "url": "https://agentseller.temu.com/1", "status_code": 200}, {"route": "/3", "url": "https://agentseller.temu.com/3", "status_code": 200}, {"route": "/412", "url": "https://agentseller.temu.com/412", "status_code": 200}, {"route": "/416", "url": "https://agentseller.temu.com/416", "status_code": 200}, {"route": "/417", "url": "https://agentseller.temu.com/417", "status_code": 200}, {"route": "/419", "url": "https://agentseller.temu.com/419", "status_code": 200}, {"route": "/8", "url": "https://agentseller.temu.com/8", "status_code": 200}, {"route": "/Book", "url": "https://agentseller.temu.com/Book", "status_code": 200}, {"route": "/EncryptedPackage", "url": "https://agentseller.temu.com/EncryptedPackage", "status_code": 200}, {"route": "/EncryptionInfo", "url": "https://agentseller.temu.com/EncryptionInfo", "status_code": 200}, {"route": "/Workbook", "url": "https://agentseller.temu.com/Workbook", "status_code": 200}, {"route": "/_VBA_PROJECT_CUR/", "url": "https://agentseller.temu.com/_VBA_PROJECT_CUR/", "status_code": 200}, {"route": "/_VBA_PROJECT_CUR/VBA/dir", "url": "https://agentseller.temu.com/_VBA_PROJECT_CUR/VBA/dir", "status_code": 200}, {"route": "/a/th", "url": "https://agentseller.temu.com/a/th", "status_code": 200}, {"route": "/activity", "url": "https://agentseller.temu.com/activity", "status_code": 200}, {"route": "/activity-entry", "url": "https://agentseller.temu.com/activity-entry", "status_code": 200}, {"route": "/activity/marketing-activity", "url": "https://agentseller.temu.com/activity/marketing-activity", "status_code": 200}, {"route": "/activity/marketing-activity/auto-sign", "url": "https://agentseller.temu.com/activity/marketing-activity/auto-sign", "status_code": 200}, {"route": "/activity/marketing-activity/detail-new", "url": "https://agentseller.temu.com/activity/marketing-activity/detail-new", "status_code": 200}, {"route": "/activity/marketing-activity/detail-new-result", "url": "https://agentseller.temu.com/activity/marketing-activity/detail-new-result", "status_code": 200}, {"route": "/activity/marketing-activity/detail/worry-free", "url": "https://agentseller.temu.com/activity/marketing-activity/detail/worry-free", "status_code": 200}, {"route": "/activity/marketing-activity/hot-girl-cloth", "url": "https://agentseller.temu.com/activity/marketing-activity/hot-girl-cloth", "status_code": 200}, {"route": "/activity/marketing-activity/log", "url": "https://agentseller.temu.com/activity/marketing-activity/log", "status_code": 200}, {"route": "/activity/marketing-activity/mdeast-subsidy", "url": "https://agentseller.temu.com/activity/marketing-activity/mdeast-subsidy", "status_code": 200}, {"route": "/activity/marketing-activity/result", "url": "https://agentseller.temu.com/activity/marketing-activity/result", "status_code": 200}, {"route": "/activity/marketing-activity/semi-detail", "url": "https://agentseller.temu.com/activity/marketing-activity/semi-detail", "status_code": 200}, {"route": "/activity/marketing-activity/spring-activity", "url": "https://agentseller.temu.com/activity/marketing-activity/spring-activity", "status_code": 200}, {"route": "/activity/marketing-tools/create-coupon", "url": "https://agentseller.temu.com/activity/marketing-tools/create-coupon", "status_code": 200}, {"route": "/activity/marketing-tools/create-mall-coupon", "url": "https://agentseller.temu.com/activity/marketing-tools/create-mall-coupon", "status_code": 200}, {"route": "/activity/marketing-tools/create-recommend-coupon", "url": "https://agentseller.temu.com/activity/marketing-tools/create-recommend-coupon", "status_code": 200}, {"route": "/acw/shipping-mgt/details", "url": "https://agentseller.temu.com/acw/shipping-mgt/details", "status_code": 200}, {"route": "/acw/shipping-mgt/operate", "url": "https://agentseller.temu.com/acw/shipping-mgt/operate", "status_code": 200}, {"route": "/acw/stocking-mgt/details", "url": "https://agentseller.temu.com/acw/stocking-mgt/details", "status_code": 200}, {"route": "/acw/stocking-mgt/operate", "url": "https://agentseller.temu.com/acw/stocking-mgt/operate", "status_code": 200}, {"route": "/agent-seller-lgst-entry", "url": "https://agentseller.temu.com/agent-seller-lgst-entry", "status_code": 200}, {"route": "/agora/conv/needReplyCount", "url": "https://agentseller.temu.com/agora/conv/needReplyCount", "status_code": 200}, {"route": "/ams-lgst-temu/", "url": "https://agentseller.temu.com/ams-lgst-temu/", "status_code": 200}, {"route": "/antman/api/flow/checkActivityPrice", "url": "https://agentseller.temu.com/antman/api/flow/checkActivityPrice", "status_code": 200}, {"route": "/antman/api/flow/commit", "url": "https://agentseller.temu.com/antman/api/flow/commit", "status_code": 200}, {"route": "/api/bg-ladyfish/mms/menu/page/feedback/entrance", "url": "https://agentseller.temu.com/api/bg-ladyfish/mms/menu/page/feedback/entrance", "status_code": 200}, {"route": "/api/bg-ladyfish/mms/menu/page/feedback/submit", "url": "https://agentseller.temu.com/api/bg-ladyfish/mms/menu/page/feedback/submit", "status_code": 200}, {"route": "/api/bg/cw/getCwTicket", "url": "https://agentseller.temu.com/api/bg/cw/getCwTicket", "status_code": 200}, {"route": "/api/bg/cw/pop/up/queryPopUpInfo", "url": "https://agentseller.temu.com/api/bg/cw/pop/up/queryPopUpInfo", "status_code": 200}, {"route": "/api/bg/cw/pop/up/saveRecommendUseErpInfo", "url": "https://agentseller.temu.com/api/bg/cw/pop/up/saveRecommendUseErpInfo", "status_code": 200}, {"route": "/api/bg/cw/redpoint", "url": "https://agentseller.temu.com/api/bg/cw/redpoint", "status_code": 200}, {"route": "/api/bg/vision/mms/gray/query", "url": "https://agentseller.temu.com/api/bg/vision/mms/gray/query", "status_code": 200}, {"route": "/api/bg/vision/mms/popup/query/content", "url": "https://agentseller.temu.com/api/bg/vision/mms/popup/query/content", "status_code": 200}, {"route": "/api/bg/vision/mms/popup/query/related_erp_list", "url": "https://agentseller.temu.com/api/bg/vision/mms/popup/query/related_erp_list", "status_code": 200}, {"route": "/api/bg/vision/mms/product/sku/match/bound/submit", "url": "https://agentseller.temu.com/api/bg/vision/mms/product/sku/match/bound/submit", "status_code": 200}, {"route": "/api/galerie/file/signature", "url": "https://agentseller.temu.com/api/galerie/file/signature", "status_code": 200}, {"route": "/api/galerie/image/signature", "url": "https://agentseller.temu.com/api/galerie/image/signature", "status_code": 200}, {"route": "/api/galerie/large_file/v1/video/upload_complete", "url": "https://agentseller.temu.com/api/galerie/large_file/v1/video/upload_complete", "status_code": 200}, {"route": "/api/galerie/large_file/v1/video/upload_init", "url": "https://agentseller.temu.com/api/galerie/large_file/v1/video/upload_init", "status_code": 200}, {"route": "/api/galerie/large_file/v1/video/upload_part", "url": "https://agentseller.temu.com/api/galerie/large_file/v1/video/upload_part", "status_code": 200}, {"route": "/api/galerie/public/signature", "url": "https://agentseller.temu.com/api/galerie/public/signature", "status_code": 200}, {"route": "/api/kiana/antman/adjust/for/flow/pop", "url": "https://agentseller.temu.com/api/kiana/antman/adjust/for/flow/pop", "status_code": 200}, {"route": "/api/kiana/gamblers/marketing/coupon/invite/query", "url": "https://agentseller.temu.com/api/kiana/gamblers/marketing/coupon/invite/query", "status_code": 200}, {"route": "/api/kiana/gamblers/marketing/coupon/min/amount", "url": "https://agentseller.temu.com/api/kiana/gamblers/marketing/coupon/min/amount", "status_code": 200}, {"route": "/api/kiana/gamblers/marketing/enroll/pop/apply", "url": "https://agentseller.temu.com/api/kiana/gamblers/marketing/enroll/pop/apply", "status_code": 200}, {"route": "/api/kiana/magnus/mms/price/query-compare-sku-info", "url": "https://agentseller.temu.com/api/kiana/magnus/mms/price/query-compare-sku-info", "status_code": 200}, {"route": "/api/kiana/magnus/mms/price/re-price-review", "url": "https://agentseller.temu.com/api/kiana/magnus/mms/price/re-price-review", "status_code": 200}, {"route": "/api/kiana/magnus/mms/price/re-price-review/click", "url": "https://agentseller.temu.com/api/kiana/magnus/mms/price/re-price-review/click", "status_code": 200}, {"route": "/api/kiana/magnus/mms/price/reviewDetail", "url": "https://agentseller.temu.com/api/kiana/magnus/mms/price/reviewDetail", "status_code": 200}, {"route": "/api/kiana/mms/magneto/price/reviewDetail", "url": "https://agentseller.temu.com/api/kiana/mms/magneto/price/reviewDetail", "status_code": 200}, {"route": "/api/phantom/", "url": "https://agentseller.temu.com/api/phantom/", "status_code": 200}, {"route": "/api/phantom/captcha_unsupport", "url": "https://agentseller.temu.com/api/phantom/captcha_unsupport", "status_code": 200}, {"route": "/api/phantom/obtain_captcha", "url": "https://agentseller.temu.com/api/phantom/obtain_captcha", "status_code": 200}, {"route": "/api/phantom/user_verify", "url": "https://agentseller.temu.com/api/phantom/user_verify", "status_code": 200}, {"route": "/api/pmm", "url": "https://agentseller.temu.com/api/pmm", "status_code": 200}, {"route": "/api/pmm/api", "url": "https://agentseller.temu.com/api/pmm/api", "status_code": 200}, {"route": "/api/pmm/defined", "url": "https://agentseller.temu.com/api/pmm/defined", "status_code": 200}, {"route": "/api/pmm/front_err", "url": "https://agentseller.temu.com/api/pmm/front_err", "status_code": 200}, {"route": "/api/pmm/front_log", "url": "https://agentseller.temu.com/api/pmm/front_log", "status_code": 200}, {"route": "/api/pmm/page", "url": "https://agentseller.temu.com/api/pmm/page", "status_code": 200}, {"route": "/api/pmm/static", "url": "https://agentseller.temu.com/api/pmm/static", "status_code": 200}, {"route": "/api/server/_stm", "url": "https://agentseller.temu.com/api/server/_stm", "status_code": 200}, {"route": "/authentication", "url": "https://agentseller.temu.com/authentication", "status_code": 200}, {"route": "/auto-orient", "url": "https://agentseller.temu.com/auto-orient", "status_code": 200}, {"route": "/batch/1", "url": "https://agentseller.temu.com/batch/1", "status_code": 200}, {"route": "/bert/api/page/info/agentSeller/pageInfo", "url": "https://agentseller.temu.com/bert/api/page/info/agentSeller/pageInfo", "status_code": 200}, {"route": "/bert/api/page/info/ams/pageInfo", "url": "https://agentseller.temu.com/bert/api/page/info/ams/pageInfo", "status_code": 200}, {"route": "/bert/api/page/info/app/pageInfo", "url": "https://agentseller.temu.com/bert/api/page/info/app/pageInfo", "status_code": 200}, {"route": "/bert/api/page/info/foreignLogistics/pageInfo", "url": "https://agentseller.temu.com/bert/api/page/info/foreignLogistics/pageInfo", "status_code": 200}, {"route": "/bert/api/page/info/foreignTms/pageInfo", "url": "https://agentseller.temu.com/bert/api/page/info/foreignTms/pageInfo", "status_code": 200}, {"route": "/bert/api/page/info/gmp/pageInfo", "url": "https://agentseller.temu.com/bert/api/page/info/gmp/pageInfo", "status_code": 200}, {"route": "/bert/api/page/info/lms/pageInfo", "url": "https://agentseller.temu.com/bert/api/page/info/lms/pageInfo", "status_code": 200}, {"route": "/bert/api/page/info/logistics/pageInfo", "url": "https://agentseller.temu.com/bert/api/page/info/logistics/pageInfo", "status_code": 200}, {"route": "/bert/api/page/info/oms/pageInfo", "url": "https://agentseller.temu.com/bert/api/page/info/oms/pageInfo", "status_code": 200}, {"route": "/bert/api/page/info/rank/pageInfo", "url": "https://agentseller.temu.com/bert/api/page/info/rank/pageInfo", "status_code": 200}, {"route": "/bert/api/page/info/tms/pageInfo", "url": "https://agentseller.temu.com/bert/api/page/info/tms/pageInfo", "status_code": 200}, {"route": "/bert/api/page/info/vms/pageInfo", "url": "https://agentseller.temu.com/bert/api/page/info/vms/pageInfo", "status_code": 200}, {"route": "/bert/api/page/info/wms/pageInfo", "url": "https://agentseller.temu.com/bert/api/page/info/wms/pageInfo", "status_code": 200}, {"route": "/bert/api/page/info/workbench/agent/pageInfo", "url": "https://agentseller.temu.com/bert/api/page/info/workbench/agent/pageInfo", "status_code": 200}, {"route": "/bert/api/page/info/workbench/pageInfo", "url": "https://agentseller.temu.com/bert/api/page/info/workbench/pageInfo", "status_code": 200}, {"route": "/bg/quick/api/merchant/rule/read", "url": "https://agentseller.temu.com/bg/quick/api/merchant/rule/read", "status_code": 200}, {"route": "/bg/quick/api/merchant/rule/unreadList", "url": "https://agentseller.temu.com/bg/quick/api/merchant/rule/unreadList", "status_code": 200}, {"route": "/bg/quick/api/merchant/rule/unreadNum", "url": "https://agentseller.temu.com/bg/quick/api/merchant/rule/unreadNum", "status_code": 200}, {"route": "/bg/staff/api/foreign/logistics/account/menu", "url": "https://agentseller.temu.com/bg/staff/api/foreign/logistics/account/menu", "status_code": 200}, {"route": "/bg/staff/api/user/permission/lms/queryUserMenu", "url": "https://agentseller.temu.com/bg/staff/api/user/permission/lms/queryUserMenu", "status_code": 200}, {"route": "/bg/staff/api/user/permission/sc/queryRoleMenuInfo", "url": "https://agentseller.temu.com/bg/staff/api/user/permission/sc/queryRoleMenuInfo", "status_code": 200}, {"route": "/bg/swift/api/auth/obtainCode", "url": "https://agentseller.temu.com/bg/swift/api/auth/obtainCode", "status_code": 200}, {"route": "/bg/swift/api/jump/token", "url": "https://agentseller.temu.com/bg/swift/api/jump/token", "status_code": 200}, {"route": "/blur/", "url": "https://agentseller.temu.com/blur/", "status_code": 200}, {"route": "/c/api/auth/c/token/obtainTicket", "url": "https://agentseller.temu.com/c/api/auth/c/token/obtainTicket", "status_code": 200}, {"route": "/c/api/auth/c/token/showJumpTemu", "url": "https://agentseller.temu.com/c/api/auth/c/token/showJumpTemu", "status_code": 200}, {"route": "/cgif/", "url": "https://agentseller.temu.com/cgif/", "status_code": 200}, {"route": "/chat-app", "url": "https://agentseller.temu.com/chat-app", "status_code": 200}, {"route": "/chat-app/contact-buyer", "url": "https://agentseller.temu.com/chat-app/contact-buyer", "status_code": 200}, {"route": "/chat-app/index", "url": "https://agentseller.temu.com/chat-app/index", "status_code": 200}, {"route": "/chat-app/service-indicator", "url": "https://agentseller.temu.com/chat-app/service-indicator", "status_code": 200}, {"route": "/chat-app/ticket-detail", "url": "https://agentseller.temu.com/chat-app/ticket-detail", "status_code": 200}, {"route": "/chat-app/ticket-list", "url": "https://agentseller.temu.com/chat-app/ticket-list", "status_code": 200}, {"route": "/chat-app/upload-template", "url": "https://agentseller.temu.com/chat-app/upload-template", "status_code": 200}, {"route": "/chat-entry", "url": "https://agentseller.temu.com/chat-entry", "status_code": 200}, {"route": "/check-compliance", "url": "https://agentseller.temu.com/check-compliance", "status_code": 200}, {"route": "/copyright-guide", "url": "https://agentseller.temu.com/copyright-guide", "status_code": 200}, {"route": "/create/category", "url": "https://agentseller.temu.com/create/category", "status_code": 200}, {"route": "/crop/", "url": "https://agentseller.temu.com/crop/", "status_code": 200}, {"route": "/crop/x", "url": "https://agentseller.temu.com/crop/x", "status_code": 200}, {"route": "/customer-consultation", "url": "https://agentseller.temu.com/customer-consultation", "status_code": 200}, {"route": "/customize-goods", "url": "https://agentseller.temu.com/customize-goods", "status_code": 200}, {"route": "/cut/", "url": "https://agentseller.temu.com/cut/", "status_code": 200}, {"route": "/data-center/goods-data", "url": "https://agentseller.temu.com/data-center/goods-data", "status_code": 200}, {"route": "/degree/", "url": "https://agentseller.temu.com/degree/", "status_code": 200}, {"route": "/dissolve/", "url": "https://agentseller.temu.com/dissolve/", "status_code": 200}, {"route": "/dx/", "url": "https://agentseller.temu.com/dx/", "status_code": 200}, {"route": "/dy/", "url": "https://agentseller.temu.com/dy/", "status_code": 200}, {"route": "/edit", "url": "https://agentseller.temu.com/edit", "status_code": 200}, {"route": "/encryption", "url": "https://agentseller.temu.com/encryption", "status_code": 200}, {"route": "/entity-edit-compliance", "url": "https://agentseller.temu.com/entity-edit-compliance", "status_code": 200}, {"route": "/entity-replace-compliance", "url": "https://agentseller.temu.com/entity-replace-compliance", "status_code": 200}, {"route": "/feedback_v2/mms/api/create/black/and/white/list", "url": "https://agentseller.temu.com/feedback_v2/mms/api/create/black/and/white/list", "status_code": 200}, {"route": "/feedback_v2/mms/api/list/not/same/reason", "url": "https://agentseller.temu.com/feedback_v2/mms/api/list/not/same/reason", "status_code": 200}, {"route": "/fill/", "url": "https://agentseller.temu.com/fill/", "status_code": 200}, {"route": "/font/", "url": "https://agentseller.temu.com/font/", "status_code": 200}, {"route": "/fontsize/", "url": "https://agentseller.temu.com/fontsize/", "status_code": 200}, {"route": "/format/", "url": "https://agentseller.temu.com/format/", "status_code": 200}, {"route": "/format/webp", "url": "https://agentseller.temu.com/format/webp", "status_code": 200}, {"route": "/galerie/business/get_signature", "url": "https://agentseller.temu.com/galerie/business/get_signature", "status_code": 200}, {"route": "/gambit/api/foreign/aml/execute/annualConfirm", "url": "https://agentseller.temu.com/gambit/api/foreign/aml/execute/annualConfirm", "status_code": 200}, {"route": "/gambit/api/mallApply/oneKeyOpenSemiMangedMall", "url": "https://agentseller.temu.com/gambit/api/mallApply/oneKeyOpenSemiMangedMall", "status_code": 200}, {"route": "/gambit/api/mallApply/querySettleProtocol", "url": "https://agentseller.temu.com/gambit/api/mallApply/querySettleProtocol", "status_code": 200}, {"route": "/get-leo-config", "url": "https://agentseller.temu.com/get-leo-config", "status_code": 200}, {"route": "/get_endpoint", "url": "https://agentseller.temu.com/get_endpoint", "status_code": 200}, {"route": "/goods", "url": "https://agentseller.temu.com/goods", "status_code": 200}, {"route": "/goods-entry", "url": "https://agentseller.temu.com/goods-entry", "status_code": 200}, {"route": "/goods/list", "url": "https://agentseller.temu.com/goods/list", "status_code": 200}, {"route": "/govern-platform", "url": "https://agentseller.temu.com/govern-platform", "status_code": 200}, {"route": "/govern-platform/entry", "url": "https://agentseller.temu.com/govern-platform/entry", "status_code": 200}, {"route": "/govern-platform/mobile-device-code/agreement", "url": "https://agentseller.temu.com/govern-platform/mobile-device-code/agreement", "status_code": 200}, {"route": "/gravity/", "url": "https://agentseller.temu.com/gravity/", "status_code": 200}, {"route": "/gravity/center", "url": "https://agentseller.temu.com/gravity/center", "status_code": 200}, {"route": "/gravity/northeast/batch/1/degree/", "url": "https://agentseller.temu.com/gravity/northeast/batch/1/degree/", "status_code": 200}, {"route": "/grayscale/1", "url": "https://agentseller.temu.com/grayscale/1", "status_code": 200}, {"route": "/h/", "url": "https://agentseller.temu.com/h/", "status_code": 200}, {"route": "/hawk/mms/course/exam/courseExamPopup", "url": "https://agentseller.temu.com/hawk/mms/course/exam/courseExamPopup", "status_code": 200}, {"route": "/hawk/mms/course/exam/queryCourseExam", "url": "https://agentseller.temu.com/hawk/mms/course/exam/queryCourseExam", "status_code": 200}, {"route": "/hawk/mms/course/exam/queryCourseExamLogDetail", "url": "https://agentseller.temu.com/hawk/mms/course/exam/queryCourseExamLogDetail", "status_code": 200}, {"route": "/hawk/mms/course/exam/submitCourseExamLog", "url": "https://agentseller.temu.com/hawk/mms/course/exam/submitCourseExamLog", "status_code": 200}, {"route": "/ignore-error/", "url": "https://agentseller.temu.com/ignore-error/", "status_code": 200}, {"route": "/images-edit", "url": "https://agentseller.temu.com/images-edit", "status_code": 200}, {"route": "/interlace/", "url": "https://agentseller.temu.com/interlace/", "status_code": 200}, {"route": "/intllgst/", "url": "https://agentseller.temu.com/intllgst/", "status_code": 200}, {"route": "/iradius/", "url": "https://agentseller.temu.com/iradius/", "status_code": 200}, {"route": "/kirogi/bg/mms/homepage/queryPopup", "url": "https://agentseller.temu.com/kirogi/bg/mms/homepage/queryPopup", "status_code": 200}, {"route": "/kirogi/bg/mms/queryPopupConfig", "url": "https://agentseller.temu.com/kirogi/bg/mms/queryPopupConfig", "status_code": 200}, {"route": "/kirogi/bg/mms/queryPopupList", "url": "https://agentseller.temu.com/kirogi/bg/mms/queryPopupList", "status_code": 200}, {"route": "/kirogi/bg/mms/savePopupByCode", "url": "https://agentseller.temu.com/kirogi/bg/mms/savePopupByCode", "status_code": 200}, {"route": "/kirogi/bg/mms/subsidyDetails", "url": "https://agentseller.temu.com/kirogi/bg/mms/subsidyDetails", "status_code": 200}, {"route": "/knowledge-guide", "url": "https://agentseller.temu.com/knowledge-guide", "status_code": 200}, {"route": "/labor", "url": "https://agentseller.temu.com/labor", "status_code": 200}, {"route": "/labor-entry", "url": "https://agentseller.temu.com/labor-entry", "status_code": 200}, {"route": "/labor/credit-card-paying", "url": "https://agentseller.temu.com/labor/credit-card-paying", "status_code": 200}, {"route": "/labor/tax-document-library", "url": "https://agentseller.temu.com/labor/tax-document-library", "status_code": 200}, {"route": "/labor/tax-settle", "url": "https://agentseller.temu.com/labor/tax-settle", "status_code": 200}, {"route": "/lgst", "url": "https://agentseller.temu.com/lgst", "status_code": 200}, {"route": "/lq/", "url": "https://agentseller.temu.com/lq/", "status_code": 200}, {"route": "/lquality/", "url": "https://agentseller.temu.com/lquality/", "status_code": 200}, {"route": "/main", "url": "https://agentseller.temu.com/main", "status_code": 200}, {"route": "/main-entry", "url": "https://agentseller.temu.com/main-entry", "status_code": 200}, {"route": "/main/aftersales/information", "url": "https://agentseller.temu.com/main/aftersales/information", "status_code": 200}, {"route": "/main/authentication", "url": "https://agentseller.temu.com/main/authentication", "status_code": 200}, {"route": "/main/business-opportunity", "url": "https://agentseller.temu.com/main/business-opportunity", "status_code": 200}, {"route": "/main/business-opportunity/goods-detail", "url": "https://agentseller.temu.com/main/business-opportunity/goods-detail", "status_code": 200}, {"route": "/main/business-opportunity/market-detail", "url": "https://agentseller.temu.com/main/business-opportunity/market-detail", "status_code": 200}, {"route": "/main/category-analysis", "url": "https://agentseller.temu.com/main/category-analysis", "status_code": 200}, {"route": "/main/course/detail", "url": "https://agentseller.temu.com/main/course/detail", "status_code": 200}, {"route": "/main/course/list", "url": "https://agentseller.temu.com/main/course/list", "status_code": 200}, {"route": "/main/evaluate-manage", "url": "https://agentseller.temu.com/main/evaluate-manage", "status_code": 200}, {"route": "/main/evaluate/evaluate-list", "url": "https://agentseller.temu.com/main/evaluate/evaluate-list", "status_code": 200}, {"route": "/main/examination/detail", "url": "https://agentseller.temu.com/main/examination/detail", "status_code": 200}, {"route": "/main/examination/process", "url": "https://agentseller.temu.com/main/examination/process", "status_code": 200}, {"route": "/main/flow-grow", "url": "https://agentseller.temu.com/main/flow-grow", "status_code": 200}, {"route": "/main/flux-analysis", "url": "https://agentseller.temu.com/main/flux-analysis", "status_code": 200}, {"route": "/main/goods-label/make", "url": "https://agentseller.temu.com/main/goods-label/make", "status_code": 200}, {"route": "/main/market-analysis", "url": "https://agentseller.temu.com/main/market-analysis", "status_code": 200}, {"route": "/main/survey", "url": "https://agentseller.temu.com/main/survey", "status_code": 200}, {"route": "/main/survey/edit", "url": "https://agentseller.temu.com/main/survey/edit", "status_code": 200}, {"route": "/mall-transfer-compliance", "url": "https://agentseller.temu.com/mall-transfer-compliance", "status_code": 200}, {"route": "/mall/account-info", "url": "https://agentseller.temu.com/mall/account-info", "status_code": 200}, {"route": "/mall/entity-info", "url": "https://agentseller.temu.com/mall/entity-info", "status_code": 200}, {"route": "/material", "url": "https://agentseller.temu.com/material", "status_code": 200}, {"route": "/material-entry", "url": "https://agentseller.temu.com/material-entry", "status_code": 200}, {"route": "/material/guide/edit", "url": "https://agentseller.temu.com/material/guide/edit", "status_code": 200}, {"route": "/message", "url": "https://agentseller.temu.com/message", "status_code": 200}, {"route": "/mms/otter/warehouse/popup/query_copywriting", "url": "https://agentseller.temu.com/mms/otter/warehouse/popup/query_copywriting", "status_code": 200}, {"route": "/mms/otter/warehouse/popup/query_popup", "url": "https://agentseller.temu.com/mms/otter/warehouse/popup/query_popup", "status_code": 200}, {"route": "/mode/1/ignore-error/", "url": "https://agentseller.temu.com/mode/1/ignore-error/", "status_code": 200}, {"route": "/newon", "url": "https://agentseller.temu.com/newon", "status_code": 200}, {"route": "/newon-entry", "url": "https://agentseller.temu.com/newon-entry", "status_code": 200}, {"route": "/newon/product-select", "url": "https://agentseller.temu.com/newon/product-select", "status_code": 200}, {"route": "/node_modules/gulp-browserify/node_modules/buffer", "url": "https://agentseller.temu.com/node_modules/gulp-browserify/node_modules/buffer", "status_code": 200}, {"route": "/node_modules/gulp-browserify/node_modules/ieee754", "url": "https://agentseller.temu.com/node_modules/gulp-browserify/node_modules/ieee754", "status_code": 200}, {"route": "/node_modules/gulp-browserify/node_modules/process", "url": "https://agentseller.temu.com/node_modules/gulp-browserify/node_modules/process", "status_code": 200}, {"route": "/open", "url": "https://agentseller.temu.com/open", "status_code": 200}, {"route": "/open-entry", "url": "https://agentseller.temu.com/open-entry", "status_code": 200}, {"route": "/open-platform", "url": "https://agentseller.temu.com/open-platform", "status_code": 200}, {"route": "/open-platform/entry", "url": "https://agentseller.temu.com/open-platform/entry", "status_code": 200}, {"route": "/open-platform/system-manage/authorize", "url": "https://agentseller.temu.com/open-platform/system-manage/authorize", "status_code": 200}, {"route": "/open/mall-decoration/editor", "url": "https://agentseller.temu.com/open/mall-decoration/editor", "status_code": 200}, {"route": "/open/market/service/all", "url": "https://agentseller.temu.com/open/market/service/all", "status_code": 200}, {"route": "/open/market/service/goods-rule/main", "url": "https://agentseller.temu.com/open/market/service/goods-rule/main", "status_code": 200}, {"route": "/open/market/service/goods-rule/order-detail", "url": "https://agentseller.temu.com/open/market/service/goods-rule/order-detail", "status_code": 200}, {"route": "/open/market/service/goods-rule/order-list", "url": "https://agentseller.temu.com/open/market/service/goods-rule/order-list", "status_code": 200}, {"route": "/open/market/service/graph/main", "url": "https://agentseller.temu.com/open/market/service/graph/main", "status_code": 200}, {"route": "/open/market/service/logistics/main", "url": "https://agentseller.temu.com/open/market/service/logistics/main", "status_code": 200}, {"route": "/open/market/service/material/main", "url": "https://agentseller.temu.com/open/market/service/material/main", "status_code": 200}, {"route": "/open/market/service/rule/main", "url": "https://agentseller.temu.com/open/market/service/rule/main", "status_code": 200}, {"route": "/open/market/service/rule/order-detail", "url": "https://agentseller.temu.com/open/market/service/rule/order-detail", "status_code": 200}, {"route": "/open/market/service/rule/order-list", "url": "https://agentseller.temu.com/open/market/service/rule/order-list", "status_code": 200}, {"route": "/open/market/service/video/order-create", "url": "https://agentseller.temu.com/open/market/service/video/order-create", "status_code": 200}, {"route": "/open/market/service/video/order-list", "url": "https://agentseller.temu.com/open/market/service/video/order-list", "status_code": 200}, {"route": "/penalty-temu/", "url": "https://agentseller.temu.com/penalty-temu/", "status_code": 200}, {"route": "/portal/", "url": "https://agentseller.temu.com/portal/", "status_code": 200}, {"route": "/q/", "url": "https://agentseller.temu.com/q/", "status_code": 200}, {"route": "/quality/", "url": "https://agentseller.temu.com/quality/", "status_code": 200}, {"route": "/quick/merchant/pop/query", "url": "https://agentseller.temu.com/quick/merchant/pop/query", "status_code": 200}, {"route": "/quick/merchant/pop/read", "url": "https://agentseller.temu.com/quick/merchant/pop/read", "status_code": 200}, {"route": "/restock/order-manage", "url": "https://agentseller.temu.com/restock/order-manage", "status_code": 200}, {"route": "/restock/plan", "url": "https://agentseller.temu.com/restock/plan", "status_code": 200}, {"route": "/restock/stocking-shipping-receipt-details", "url": "https://agentseller.temu.com/restock/stocking-shipping-receipt-details", "status_code": 200}, {"route": "/restock/stocking-shipping-receipt/calculate", "url": "https://agentseller.temu.com/restock/stocking-shipping-receipt/calculate", "status_code": 200}, {"route": "/restock/stocking-shipping-receipt/operate", "url": "https://agentseller.temu.com/restock/stocking-shipping-receipt/operate", "status_code": 200}, {"route": "/rotate/", "url": "https://agentseller.temu.com/rotate/", "status_code": 200}, {"route": "/rq/", "url": "https://agentseller.temu.com/rq/", "status_code": 200}, {"route": "/rquality/", "url": "https://agentseller.temu.com/rquality/", "status_code": 200}, {"route": "/rradius/", "url": "https://agentseller.temu.com/rradius/", "status_code": 200}, {"route": "/rule-center", "url": "https://agentseller.temu.com/rule-center", "status_code": 200}, {"route": "/sample", "url": "https://agentseller.temu.com/sample", "status_code": 200}, {"route": "/sample-entry", "url": "https://agentseller.temu.com/sample-entry", "status_code": 200}, {"route": "/scrop/", "url": "https://agentseller.temu.com/scrop/", "status_code": 200}, {"route": "/settle/site-main", "url": "https://agentseller.temu.com/settle/site-main", "status_code": 200}, {"route": "/sharpen/", "url": "https://agentseller.temu.com/sharpen/", "status_code": 200}, {"route": "/stock", "url": "https://agentseller.temu.com/stock", "status_code": 200}, {"route": "/stock-edit", "url": "https://agentseller.temu.com/stock-edit", "status_code": 200}, {"route": "/stock-entry", "url": "https://agentseller.temu.com/stock-entry", "status_code": 200}, {"route": "/stock/fully-mgt/order-manage", "url": "https://agentseller.temu.com/stock/fully-mgt/order-manage", "status_code": 200}, {"route": "/stock/fully-mgt/order-manage-custom", "url": "https://agentseller.temu.com/stock/fully-mgt/order-manage-custom", "status_code": 200}, {"route": "/stock/fully-mgt/order-manage-urgency", "url": "https://agentseller.temu.com/stock/fully-mgt/order-manage-urgency", "status_code": 200}, {"route": "/stock/fully-mgt/production-suggestion", "url": "https://agentseller.temu.com/stock/fully-mgt/production-suggestion", "status_code": 200}, {"route": "/survey", "url": "https://agentseller.temu.com/survey", "status_code": 200}, {"route": "/survey/edit", "url": "https://agentseller.temu.com/survey/edit", "status_code": 200}, {"route": "/tai/api/heartbeat/key", "url": "https://agentseller.temu.com/tai/api/heartbeat/key", "status_code": 200}, {"route": "/text/", "url": "https://agentseller.temu.com/text/", "status_code": 200}, {"route": "/thumbnail", "url": "https://agentseller.temu.com/thumbnail", "status_code": 200}, {"route": "/thumbnail/", "url": "https://agentseller.temu.com/thumbnail/", "status_code": 200}, {"route": "/thumbnail/60x", "url": "https://agentseller.temu.com/thumbnail/60x", "status_code": 200}, {"route": "/thumbnail/x", "url": "https://agentseller.temu.com/thumbnail/x", "status_code": 200}, {"route": "/top-sale-cate", "url": "https://agentseller.temu.com/top-sale-cate", "status_code": 200}, {"route": "/top-sale-high-yield", "url": "https://agentseller.temu.com/top-sale-high-yield", "status_code": 200}, {"route": "/top-sale-stock", "url": "https://agentseller.temu.com/top-sale-stock", "status_code": 200}, {"route": "/visage-agent-seller/product/skc/pageQuery", "url": "https://agentseller.temu.com/visage-agent-seller/product/skc/pageQuery", "status_code": 200}, {"route": "/w/", "url": "https://agentseller.temu.com/w/", "status_code": 200}, {"route": "/wms", "url": "https://agentseller.temu.com/wms", "status_code": 200}, {"route": "/wms-entry", "url": "https://agentseller.temu.com/wms-entry", "status_code": 200}, {"route": "/x", "url": "https://agentseller.temu.com/x", "status_code": 200}], "working_apis": [{"endpoint": "/api/bg/cw/getCwProviderList", "method": "POST", "status_code": 200, "response_type": "JSON", "sample_response": "{'success': True, 'errorCode': 1000000, 'errorMsg': None, 'result': {'labelList': ['适用高重量段', '大件仓', '价格更优', '支持平台物流'], 'regionNameList': ['美国', '加拿大'], 'cwProviderInfoDTOList': [{'cwProviderCode': 'SP..."}, {"endpoint": "/api/bg/cw/mallCwTokenAuthorization", "method": "POST", "status_code": 200, "response_type": "JSON", "sample_response": "{'success': False, 'errorCode': 3000000, 'errorMsg': 'service provider code cannot be empty,The authentication warehouse authorization token cannot be empty,The shipper code of the certified warehouse..."}, {"endpoint": "/api/charge_back/query/status/count", "method": "GET", "status_code": 200, "response_type": "JSON", "sample_response": "{'success': True, 'result': {'charge_back_status_v2_count_map': {'0': 0, '1': 0, '2': 0, '3': 0, '99': 0}, 'un_read_num': 0}, 'error_code': 1000000, 'error_msg': None}"}, {"endpoint": "/api/galerie/cos_large_file/upload_complete", "method": "POST", "status_code": 200, "response_type": "JSON", "sample_response": "{'error_code': 40003, 'error_msg': '错误的请求参数: 签名为空'}"}, {"endpoint": "/api/galerie/cos_large_file/upload_init", "method": "POST", "status_code": 200, "response_type": "JSON", "sample_response": "{'success': False, 'result': {'error_code': 48005, 'error_msg': '签名已过期，请重试'}}"}, {"endpoint": "/api/galerie/cos_large_file/upload_part", "method": "POST", "status_code": 200, "response_type": "JSON", "sample_response": "{'error_code': 40003, 'error_msg': '错误的请求参数: 签名或块号或文件为空'}"}, {"endpoint": "/api/kiana/mms/magneto/price/query-compare-sku-info", "method": "POST", "status_code": 200, "response_type": "JSON", "sample_response": "{'result': None, 'success': False, 'errorCode': 3000000, 'errorMsg': '不能为null;不能为null'}"}, {"endpoint": "/api/seller/auth/loginByCode", "method": "POST", "status_code": 200, "response_type": "JSON", "sample_response": "{'success': False, 'errorCode': 3000000, 'errorMsg': 'System Exception', 'result': None}"}, {"endpoint": "/api/seller/auth/logout", "method": "POST", "status_code": 200, "response_type": "JSON", "sample_response": "{'success': True, 'errorCode': 1000000, 'errorMsg': None, 'result': None}"}, {"endpoint": "/api/server/_stm", "method": "GET", "status_code": 200, "response_type": "JSON", "sample_response": "{'server_time': 1754115547526}"}], "summary": {"total_routes_discovered": 375, "total_apis_discovered": 79, "total_menu_items": 1703, "total_components": 63, "accessible_routes_count": 278, "working_apis_count": 10}}