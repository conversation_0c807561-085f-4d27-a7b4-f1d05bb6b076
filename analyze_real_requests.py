#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import os
import re

class TemuRequestAnalyzer:
    def __init__(self):
        # 禁用代理
        os.environ.pop('HTTP_PROXY', None)
        os.environ.pop('HTTPS_PROXY', None)
        os.environ.pop('http_proxy', None)
        os.environ.pop('https_proxy', None)
        
        self.session = requests.Session()
        proxies = {'http': None, 'https': None}
        self.session.proxies.update(proxies)
        self.session.trust_env = False
        
        # 设置cookie
        cookie_string = "api_uid=Ct4kjmiNlzU98QBGDn3oAg==; _nano_fp=Xpmyn0golpC8XqT8X9_v~JX55DvIt6xeNJtDofAJ; _bee=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; njrpl=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; dilx=MchfnouEwE7NM0Un4s5iO; hfsc=L3yPfIgw4Dbw15/MeQ==; seller_temp=N_eyJ0IjoiUXlzUVZhMHlIMTcxb1hFemhoUlpMWlA5dkVoUzJoRlFiSjZxRTJOYWVldnFjZWt1aGU2cTV4VWsrY0ZqUWdJU2t5dEI1RVJkUjdXT0xJc21rWmhjVHc9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyNDI3ODU0ODg1MTkwMn0=; mallid=634418224371052"
        
        # 解析cookie字符串
        for cookie in cookie_string.split('; '):
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                self.session.cookies.set(name, value, domain='.temu.com')
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://seller.temu.com/',
            'Origin': 'https://seller.temu.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        })
    
    def try_get_requests(self):
        """尝试GET请求而不是POST"""
        print("🔍 尝试GET请求...")
        
        get_endpoints = [
            '/api/pepino/account/info/get',
            '/api/bg/christiaan/pc/navigation/info',
            '/mms/floyd/account/base_info',
            '/api/bg/pawpaw/auth/check',
            '/api/server/_stm'
        ]
        
        for endpoint in get_endpoints:
            try:
                print(f"\n尝试GET: {endpoint}")
                response = self.session.get(f"https://seller.temu.com{endpoint}", timeout=30)
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'application/json' in content_type:
                        try:
                            data = response.json()
                            print(f"✅ JSON响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                            return True, endpoint, data
                        except json.JSONDecodeError:
                            print("响应不是有效的JSON")
                    else:
                        print(f"Content-Type: {content_type}")
                        if len(response.text) < 1000:
                            print(f"响应内容: {response.text}")
                        else:
                            print(f"响应内容长度: {len(response.text)}")
                
            except Exception as e:
                print(f"请求失败: {str(e)}")
        
        return False, None, None
    
    def try_different_domains(self):
        """尝试不同的域名"""
        print("\n🌐 尝试不同的域名...")
        
        domains = [
            'https://seller.temu.com',
            'https://api.temu.com',
            'https://mms.temu.com',
            'https://bg.temu.com'
        ]
        
        endpoints = [
            '/api/pepino/account/info/get',
            '/api/bg/christiaan/pc/navigation/info'
        ]
        
        for domain in domains:
            for endpoint in endpoints:
                try:
                    print(f"\n尝试: {domain}{endpoint}")
                    
                    # 尝试GET
                    response = self.session.get(f"{domain}{endpoint}", timeout=30)
                    print(f"GET 状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        content_type = response.headers.get('content-type', '')
                        if 'application/json' in content_type:
                            try:
                                data = response.json()
                                print(f"✅ GET JSON响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                                return True, f"{domain}{endpoint}", data
                            except json.JSONDecodeError:
                                pass
                    
                    # 尝试POST
                    response = self.session.post(f"{domain}{endpoint}", json={}, timeout=30)
                    print(f"POST 状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        content_type = response.headers.get('content-type', '')
                        if 'application/json' in content_type:
                            try:
                                data = response.json()
                                print(f"✅ POST JSON响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                                return True, f"{domain}{endpoint}", data
                            except json.JSONDecodeError:
                                pass
                
                except Exception as e:
                    print(f"请求失败: {str(e)}")
        
        return False, None, None
    
    def analyze_main_page(self):
        """分析主页面，寻找API调用线索"""
        print("\n📄 分析主页面...")
        
        try:
            # 访问主页
            response = self.session.get("https://seller.temu.com/", timeout=30)
            print(f"主页状态码: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                
                # 查找可能的API配置
                api_patterns = [
                    r'apiUrl["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                    r'baseUrl["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                    r'API_BASE["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                    r'["\']https?://[^"\']*api[^"\']*["\']',
                    r'fetch\s*\(\s*["\']([^"\']*api[^"\']*)["\']'
                ]
                
                found_apis = set()
                for pattern in api_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    for match in matches:
                        if isinstance(match, tuple):
                            match = match[0] if match else ""
                        if match and len(match) > 5:
                            found_apis.add(match)
                
                if found_apis:
                    print("找到的API配置:")
                    for api in sorted(found_apis):
                        print(f"  - {api}")
                
                # 查找token或认证信息
                auth_patterns = [
                    r'token["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                    r'authorization["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                    r'x-auth["\']?\s*[:=]\s*["\']([^"\']+)["\']'
                ]
                
                found_tokens = set()
                for pattern in auth_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    for match in matches:
                        if isinstance(match, tuple):
                            match = match[0] if match else ""
                        if match and len(match) > 10:
                            found_tokens.add(match)
                
                if found_tokens:
                    print("找到的认证信息:")
                    for token in found_tokens:
                        print(f"  - {token[:50]}...")
                
                # 保存页面内容
                with open("main_page_authenticated.html", 'w', encoding='utf-8') as f:
                    f.write(content)
                print("主页内容已保存到: main_page_authenticated.html")
                
        except Exception as e:
            print(f"分析主页失败: {str(e)}")
    
    def try_with_additional_headers(self):
        """尝试添加额外的请求头"""
        print("\n🔧 尝试添加额外的请求头...")
        
        # 常见的API请求头
        additional_headers = [
            {'X-Requested-With': 'XMLHttpRequest'},
            {'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest'},
            {'Accept': 'application/json', 'Content-Type': 'application/json'},
            {'X-API-Version': '1.0'},
            {'X-Client': 'web'},
            {'X-Platform': 'seller'}
        ]
        
        endpoint = '/api/pepino/account/info/get'
        
        for headers in additional_headers:
            try:
                print(f"\n尝试请求头: {headers}")
                
                # 临时添加请求头
                old_headers = self.session.headers.copy()
                self.session.headers.update(headers)
                
                response = self.session.post(f"https://seller.temu.com{endpoint}", json={}, timeout=30)
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'application/json' in content_type:
                        try:
                            data = response.json()
                            print(f"✅ 成功响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                            return True, endpoint, data
                        except json.JSONDecodeError:
                            pass
                
                # 恢复原始请求头
                self.session.headers = old_headers
                
            except Exception as e:
                print(f"请求失败: {str(e)}")
                # 恢复原始请求头
                self.session.headers = old_headers
        
        return False, None, None

def main():
    print("🔍 开始分析Temu API请求...")
    
    analyzer = TemuRequestAnalyzer()
    
    # 分析主页面
    analyzer.analyze_main_page()
    
    # 尝试GET请求
    success, endpoint, data = analyzer.try_get_requests()
    if success:
        print(f"\n🎉 找到有效的GET端点: {endpoint}")
        return
    
    # 尝试不同域名
    success, endpoint, data = analyzer.try_different_domains()
    if success:
        print(f"\n🎉 找到有效的端点: {endpoint}")
        return
    
    # 尝试额外请求头
    success, endpoint, data = analyzer.try_with_additional_headers()
    if success:
        print(f"\n🎉 找到有效的请求头组合: {endpoint}")
        return
    
    print("\n❌ 未找到有效的API端点")
    print("\n可能的原因:")
    print("1. Cookie已过期，需要重新登录")
    print("2. 需要额外的认证参数（如CSRF token）")
    print("3. API需要特殊的请求格式")
    print("4. 网站使用了反爬虫机制")

if __name__ == "__main__":
    main()
