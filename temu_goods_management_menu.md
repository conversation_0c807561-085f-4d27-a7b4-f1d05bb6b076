# Temu商品管理二级菜单结构

## 概述
基于对Temu卖家后台的系统性探索，商品管理模块包含多个二级菜单和功能页面。由于采用单页应用(SPA)架构，所有路由都由前端JavaScript处理。

## 🛍️ 商品管理主要二级菜单

### 1. 商品列表管理
- **路由**: `/goods` 和 `/goods/list`
- **功能**: 商品列表查看、搜索、筛选
- **描述**: 查看和管理所有商品的主要入口

### 2. 产品管理
- **路由**: `/products` 和 `/product`
- **功能**: 产品信息管理、编辑
- **描述**: 产品详细信息的管理界面

### 3. 商品项目管理
- **路由**: `/items`
- **功能**: 商品项目列表管理
- **描述**: 管理具体的商品项目和SKU

### 4. 定制商品
- **路由**: `/customize-goods`
- **功能**: 定制商品管理
- **描述**: 处理定制化商品的特殊管理需求

### 5. 图片编辑
- **路由**: `/images-edit`
- **功能**: 商品图片编辑和管理
- **描述**: 商品图片的上传、编辑、优化功能

### 6. 商品数据中心
- **路由**: `/data-center/goods-data`
- **功能**: 商品数据分析和统计
- **描述**: 商品相关的数据分析和报表

### 7. 商品标签制作
- **路由**: `/main/goods-label/make`
- **功能**: 商品标签设计和制作
- **描述**: 为商品创建和管理标签

### 8. 商机商品详情
- **路由**: `/main/business-opportunity/goods-detail`
- **功能**: 商机相关的商品详细信息
- **描述**: 查看商机推荐的商品详情

## 🆕 新品选品相关

### 9. 产品选择
- **路由**: `/newon/product-select`
- **功能**: 新品选品工具
- **描述**: 帮助卖家选择有潜力的新产品

### 10. 产品SKC查询
- **路由**: `/visage-agent-seller/product/skc/pageQuery`
- **功能**: 产品SKC信息查询
- **描述**: 查询产品的SKC(Stock Keeping Code)信息

## 🛒 开放平台商品服务

### 11. 商品规则主页
- **路由**: `/open/market/service/goods-rule/main`
- **功能**: 商品规则管理主页
- **描述**: 管理商品相关的规则和政策

### 12. 商品规则订单详情
- **路由**: `/open/market/service/goods-rule/order-detail`
- **功能**: 商品规则相关订单详情
- **描述**: 查看商品规则服务的订单详情

### 13. 商品规则订单列表
- **路由**: `/open/market/service/goods-rule/order-list`
- **功能**: 商品规则订单列表
- **描述**: 管理商品规则相关的订单列表

## 📊 商品相关的API端点

### 价格管理API
- `/api/kiana/mms/magneto/price/query-compare-sku-info` - 价格对比查询
- `/api/kiana/magnus/mms/price/reviewDetail` - 价格审核详情

### 商品SKU管理API
- `/api/bg/vision/mms/product/sku/match/bound/submit` - SKU匹配绑定
- `/api/bg/vision/mms/product/sku/relate/batch_switch_shipping_mode` - 批量切换物流模式
- `/api/bg/vision/mms/product/sku/relate/query/shipping_mode_product_skc` - 查询物流模式产品SKC

### 库存管理API
- `/api/kiana/firestar/unsold/stock/pageQueryMallWaitHandleTaskUnsoldProduct` - 查询待处理滞销商品
- `/api/kiana/firestar/unsold/stock/queryMallWaitHandleTask` - 查询待处理任务
- `/api/kiana/firestar/unsold/stock/supplierConfirmUnsoldStockHandleMethod` - 供应商确认滞销库存处理方法

### 商品邀请和推广API
- `/api/kiana/fenrir/CompetitorGoodsInvitationMmsService/cmpGoodsMmsPage` - 竞品商品邀请页面
- `/api/kiana/fenrir/FullChanceGoodsInvitationMmsService/fullChanceInvitationPop` - 全机会商品邀请弹窗
- `/api/kiana/gamblers/marketing/enroll/pop/product/list` - 营销报名产品列表

## 🔧 功能特点

### 1. 商品生命周期管理
- **新品选择**: 通过选品工具发现潜力商品
- **商品上架**: 完整的商品信息录入和管理
- **价格管理**: 动态价格调整和竞争分析
- **库存管理**: 实时库存监控和滞销处理
- **数据分析**: 商品表现数据分析和优化建议

### 2. 多维度商品管理
- **基础信息**: 商品名称、描述、分类等
- **图片管理**: 商品图片上传、编辑、优化
- **SKU管理**: 多规格商品的SKU管理
- **价格策略**: 定价、促销、竞争分析
- **物流配置**: 物流模式选择和配置

### 3. 智能化功能
- **商机推荐**: 基于数据分析的商品推荐
- **价格优化**: 智能价格调整建议
- **库存预警**: 滞销商品预警和处理建议
- **竞品分析**: 竞争对手商品分析

## 📱 入口页面

### 主要入口
- **商品管理入口**: `/goods-entry`
- **通用商品页面**: `/goods`
- **产品管理页面**: `/products`

## 🎯 使用建议

1. **新卖家**: 从 `/newon/product-select` 开始选品
2. **商品上架**: 使用 `/goods` 进行商品管理
3. **图片优化**: 通过 `/images-edit` 优化商品图片
4. **数据分析**: 利用 `/data-center/goods-data` 分析商品表现
5. **价格优化**: 关注价格相关API的数据反馈

## 🔄 工作流程

```
选品 → 商品录入 → 图片处理 → 价格设置 → 库存管理 → 数据分析 → 优化调整
  ↓        ↓         ↓         ↓         ↓         ↓         ↓
newon → goods → images-edit → price → stock → data-center → optimize
```

---
*更新时间: 2025-08-02 14:27*
*数据来源: Temu卖家后台系统性探索*
