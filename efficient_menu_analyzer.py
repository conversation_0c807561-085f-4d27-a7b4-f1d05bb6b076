#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import os
from datetime import datetime

class EfficientMenuAnalyzer:
    def __init__(self):
        # 禁用代理
        os.environ.pop('HTTP_PROXY', None)
        os.environ.pop('HTTPS_PROXY', None)
        os.environ.pop('http_proxy', None)
        os.environ.pop('https_proxy', None)
        
        self.session = requests.Session()
        proxies = {'http': None, 'https': None}
        self.session.proxies.update(proxies)
        self.session.trust_env = False
        
        # 设置cookie
        cookie_string = "api_uid=Ct4kjmiNlzU98QBGDn3oAg==; _nano_fp=Xpmyn0golpC8XqT8X9_v~JX55DvIt6xeNJtDofAJ; _bee=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; njrpl=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; dilx=MchfnouEwE7NM0Un4s5iO; hfsc=L3yPfIgw4Dbw15/MeQ==; seller_temp=N_eyJ0IjoiUXlzUVZhMHlIMTcxb1hFemhoUlpMWlA5dkVoUzJoRlFiSjZxRTJOYWVldnFjZWt1aGU2cTV4VWsrY0ZqUWdJU2t5dEI1RVJkUjdXT0xJc21rWmhjVHc9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyNDI3ODU0ODg1MTkwMn0=; mallid=634418224371052"
        
        # 解析cookie字符串
        for cookie in cookie_string.split('; '):
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                self.session.cookies.set(name, value, domain='.temu.com')
                self.session.cookies.set(name, value, domain='.agentseller.temu.com')
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Connection': 'keep-alive',
            'Referer': 'https://agentseller.temu.com/',
            'anti-content': '0aqAfxnYsyPdJgd9Q2I8qlOivTg2VWUwZNpNM3Hc8XcXvoD10n6YmtiazyAOihcFiOvnaiZsdwfnts96iq_CZoPp9t0ZZ2rY0ba41PQgQHTAZUPV-kB6k7MZQNWWKaq8CWlReXtZOKdj0e0vH-qme1kI_FO7_PMZqpyopUPTRZ9maP-YAuDe-9kWD83XJa4xyvFzglM1FkCdXtbmCytLPQthBX4hE0aTJpwNwOASpmux89n67EHo_KLqFBBN3uKLZWNC4tL9POo6rYGGj9A6EuKzgB6b2kX5nGdGqPBui9hK9_qL0O_V0WSk8pIVZQoWEx-3HCFl6Ys3xdscPCRfTnos7y4qH2Oi0g32E8TiEY8SIHjvIild8cmyTc_UGwOtrl7bR_gEOCewuldnBHIACNDnJ7ldQKXCrQ_e-uQJtr01VtJ1AJ7jfunRwJGqKEkTw9NCOmONefDNhFsp3yTGJSsoAJ6L8XAq9uZiiLjpHC3nuunH-krjki1-HxfGXRblF_5YRqGXfgSP89HJN2CocuOEGPRDxYpH4anLv4lOkUjfJI1zERQmDzywnHQrBgYnkdoqdSNeuExozpky2vlNgCHhJLEoJt7MsD8OcwuXdhQB7q4ytxCslcl_568YubpbeCSVMKDDvizZEpOUnUv4f8ir3A08088_3uvC3nrr4pzyGVH1LEac5nSAyQ6upzZiw8a_bWjnbmX5aFPn0DA1fL1BskDuH8Qd7N96orM9k7dPXH8',
            'mallid': '634418224371052'
        })
        
        self.base_url = "https://agentseller.temu.com"
    
    def get_menu_data_directly(self):
        """直接调用菜单API获取结构化数据"""
        print("🎯 方法1: 直接获取菜单数据")
        
        # 尝试几个可能的菜单API
        menu_apis = [
            '/api/seller/auth/menu',
            '/api/bg-ladyfish/mms/menu/page/feedback/entrance',
            '/api/server/_stm',
            '/bg/staff/api/user/permission/sc/queryRoleMenuInfo',
            '/bg/staff/api/user/permission/lms/queryUserMenu'
        ]
        
        menu_data = {}
        
        for api in menu_apis:
            url = f"{self.base_url}{api}"
            print(f"  测试菜单API: {api}")
            
            try:
                # 尝试POST请求
                response = self.session.post(url, json={}, timeout=10)
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'application/json' in content_type:
                        try:
                            data = response.json()
                            print(f"    ✅ 成功获取数据")
                            menu_data[api] = data
                            
                            # 打印数据结构预览
                            if isinstance(data, dict):
                                keys = list(data.keys())[:5]
                                print(f"    📋 数据字段: {keys}")
                            
                        except Exception as e:
                            print(f"    ⚠️ JSON解析失败: {str(e)}")
                    else:
                        print(f"    ⚠️ 非JSON响应")
                else:
                    print(f"    ❌ 状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"    ❌ 请求失败: {str(e)}")
        
        return menu_data
    
    def get_key_business_apis(self):
        """获取关键业务API数据"""
        print("\n🔑 方法2: 获取关键业务数据")
        
        # 关键业务API
        business_apis = [
            # 商品相关
            {
                'name': '商品数据',
                'endpoint': '/api/kiana/mms/robin/searchForSemiSupplier',
                'method': 'POST',
                'params': {'pageNum': 1, 'pageSize': 20}
            },
            # 订单相关
            {
                'name': '订单统计',
                'endpoint': '/api/charge_back/query/status/count',
                'method': 'GET',
                'params': {}
            },
            # 库存相关
            {
                'name': '库存信息',
                'endpoint': '/api/kiana/firestar/unsold/stock/queryMallWaitHandleTask',
                'method': 'POST',
                'params': {}
            },
            # 价格相关
            {
                'name': '价格信息',
                'endpoint': '/api/kiana/mms/magneto/price/reviewDetail',
                'method': 'POST',
                'params': {}
            }
        ]
        
        business_data = {}
        
        for api_info in business_apis:
            name = api_info['name']
            endpoint = api_info['endpoint']
            method = api_info['method']
            params = api_info['params']
            
            url = f"{self.base_url}{endpoint}"
            print(f"  获取{name}: {endpoint}")
            
            try:
                if method == 'GET':
                    response = self.session.get(url, params=params, timeout=10)
                else:
                    response = self.session.post(url, json=params, timeout=10)
                
                if response.status_code == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'application/json' in content_type:
                        try:
                            data = response.json()
                            print(f"    ✅ 成功获取{name}数据")
                            business_data[name] = data
                            
                            # 分析数据结构
                            if isinstance(data, dict):
                                if 'result' in data:
                                    result = data['result']
                                    if isinstance(result, dict):
                                        print(f"    📊 数据字段: {list(result.keys())[:3]}")
                                    elif isinstance(result, list):
                                        print(f"    📊 数据条数: {len(result)}")
                            
                        except Exception as e:
                            print(f"    ⚠️ JSON解析失败: {str(e)}")
                    else:
                        print(f"    ⚠️ 非JSON响应")
                else:
                    print(f"    ❌ 状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"    ❌ 请求失败: {str(e)}")
        
        return business_data
    
    def get_focused_menu_structure(self):
        """获取重点关注的菜单结构"""
        print("\n🎯 方法3: 重点菜单结构分析")
        
        # 重点关注的功能模块
        focus_modules = {
            '商品管理': ['/goods', '/products', '/newon/product-select'],
            '订单管理': ['/orders', '/sales'],
            '库存管理': ['/stock', '/inventory'],
            '数据分析': ['/analytics', '/data-center/goods-data'],
            '客服管理': ['/chat-app', '/customer-consultation'],
            '财务管理': ['/finance', '/payment'],
            '物流管理': ['/lgst', '/shipping'],
            '营销活动': ['/activity', '/marketing']
        }
        
        module_status = {}
        
        for module_name, routes in focus_modules.items():
            print(f"  检查模块: {module_name}")
            module_info = {
                'name': module_name,
                'routes': routes,
                'accessible_routes': [],
                'status': 'unknown'
            }
            
            accessible_count = 0
            for route in routes:
                url = f"{self.base_url}{route}"
                try:
                    response = self.session.get(url, timeout=5)
                    if response.status_code == 200:
                        module_info['accessible_routes'].append(route)
                        accessible_count += 1
                except:
                    pass
            
            if accessible_count == len(routes):
                module_info['status'] = 'fully_accessible'
                print(f"    ✅ 完全可访问 ({accessible_count}/{len(routes)})")
            elif accessible_count > 0:
                module_info['status'] = 'partially_accessible'
                print(f"    ⚠️ 部分可访问 ({accessible_count}/{len(routes)})")
            else:
                module_info['status'] = 'not_accessible'
                print(f"    ❌ 不可访问 ({accessible_count}/{len(routes)})")
            
            module_status[module_name] = module_info
        
        return module_status
    
    def generate_summary_report(self, menu_data, business_data, module_status):
        """生成简洁的总结报告"""
        print("\n📋 生成总结报告")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        summary = {
            'timestamp': timestamp,
            'analysis_method': 'efficient_focused_analysis',
            'menu_apis': {
                'total_tested': len(menu_data),
                'successful': len([k for k, v in menu_data.items() if v]),
                'data': menu_data
            },
            'business_apis': {
                'total_tested': len(business_data),
                'successful': len([k for k, v in business_data.items() if v]),
                'data': business_data
            },
            'module_analysis': {
                'total_modules': len(module_status),
                'fully_accessible': len([m for m in module_status.values() if m['status'] == 'fully_accessible']),
                'partially_accessible': len([m for m in module_status.values() if m['status'] == 'partially_accessible']),
                'not_accessible': len([m for m in module_status.values() if m['status'] == 'not_accessible']),
                'details': module_status
            },
            'recommendations': self.generate_recommendations(menu_data, business_data, module_status)
        }
        
        # 保存报告
        filename = f"temu_efficient_analysis_{timestamp}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"  💾 报告已保存: {filename}")
        
        # 打印简洁总结
        print(f"\n🎉 分析完成!")
        print(f"  📊 菜单API: {summary['menu_apis']['successful']}/{summary['menu_apis']['total_tested']} 成功")
        print(f"  🔑 业务API: {summary['business_apis']['successful']}/{summary['business_apis']['total_tested']} 成功")
        print(f"  🎯 功能模块: {summary['module_analysis']['fully_accessible']} 完全可用, {summary['module_analysis']['partially_accessible']} 部分可用")
        
        return summary
    
    def generate_recommendations(self, menu_data, business_data, module_status):
        """生成使用建议"""
        recommendations = []
        
        # 基于可用模块的建议
        fully_accessible = [name for name, info in module_status.items() if info['status'] == 'fully_accessible']
        
        if '商品管理' in fully_accessible:
            recommendations.append("✅ 商品管理功能完全可用，建议优先使用商品相关API进行数据提取")
        
        if '数据分析' in fully_accessible:
            recommendations.append("📊 数据分析模块可用，建议定期获取商品和销售数据")
        
        if business_data:
            recommendations.append("🔑 关键业务API可用，建议建立定时数据采集机制")
        
        if not menu_data:
            recommendations.append("⚠️ 菜单API访问受限，建议通过页面路由方式获取功能信息")
        
        return recommendations

def main():
    print("🚀 Temu高效菜单分析工具")
    print("🎯 采用多种方法快速获取关键信息")
    
    analyzer = EfficientMenuAnalyzer()
    
    # 方法1: 直接获取菜单数据
    menu_data = analyzer.get_menu_data_directly()
    
    # 方法2: 获取关键业务数据
    business_data = analyzer.get_key_business_apis()
    
    # 方法3: 重点模块分析
    module_status = analyzer.get_focused_menu_structure()
    
    # 生成总结报告
    summary = analyzer.generate_summary_report(menu_data, business_data, module_status)
    
    return summary

if __name__ == "__main__":
    main()
