<!doctype html><html lang="zh"><head><meta charset="utf-8"/><meta name="keywords" content=""/><meta name="description" content=""/><meta name="theme-color" content="#000000"/><meta name="viewport" content="width=device-width,initial-scale=1,shrink-to-fit=no"/><link rel="shortcut icon" href="https://bstatic.kwcdn.com/static/files/sc/52de696d-7507-40cb-b983-9273182c1c93.ico"/><title>Temu Seller Central</title><style>html, body, #sca-container-root {
      height: 100%;
    }</style><script>;(function() {
              try {
              if(location.host.indexOf('.temudemo.net') !== -1) return
               var scene = 'US_PUBLIC'
               var originConfig = {
                  US_PUBLIC: 'https://www.pftk.temu.com',
                  EU_PUBLIC: 'https://eu.pftk.temu.com',
               }
               var crc32 = function(r) {
                for (var a, o = [], c = 0; c < 256; c++) {
                  a = c
                  for (var f = 0; f < 8; f++) a = 1 & a ? 3988292384 ^ (a >>> 1) : a >>> 1
                  o[c] = a
                }
                for (var n = -1, t = 0; t < r.length; t++) n = (n >>> 8) ^ o[255 & (n ^ r.charCodeAt(t))]
                return (-1 ^ n) >>> 0
              },
              uuidv4 = function(a) {
                return a
                  ? (a ^ ((Math.random() * 16) >> (a / 4))).toString(16)
                  : ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, uuidv4)
              },
              isProd = true,
              includeDomains = [],
              excludeDomains = [],
              pmmAppIds =  [
                {
                  name: 'bgb-sca-main',
                  activeRule: '/main',
                  appId: '101138'
                },
                {
                  name: 'bgb-sca-settlement',
                  activeRule: '/labor',
                  appId: '101275'
                },
                {
                  name: 'bg-merchant-chat',
                  activeRule: '/chat-app',
                  appId: '101269'
                },
                {
                  name: 'bg-agent-seller-lgst',
                  activeRule: '/lgst',
                  appId: '101354'
                },
                {
                  name: 'temu-sca-stock',
                  activeRule: '/stock',
                  appId: '101643'
                },
              ],
              origin = originConfig[scene], 
              random = Number((Math.random() + '').slice(3, 9)),
              connection = navigator.connection || {},
              network =
                connection.type === 'wifi'
                  ? 1
                  : connection.type === 'cellular'
                  ? { '2g': 2, '3g': 3, '4g': 4 }[connection.effectiveType]
                  : 0, 
              entry = (window.performance && performance.getEntriesByName(location.href)[0]) || {},
              now = Date.now(), 
              nows = ((now / 1000) | 0) + '',
              getPerformance = function(entry) {
                return Object.keys(entry.toJSON ? entry.toJSON() : {}).reduce(function(acc, curr) {
                  acc['performance.' + curr] = entry[curr] + ''
                  return acc
                }, {})
              },
              iris_context_env = (document.cookie.match('iris_context_env=([^;]+)') || [])[1] || '',
              commonTags = {}
        
            try {
              commonTags = Object.assign(commonTags, {
                releaseVersion: ''
              })
            } catch (e) {}
        
          
            ;/iP(hone|od|ad)/.test(navigator.platform) &&
              parseInt(navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/) || [], 10) < 13 &&
              (new Image().src = origin)
      
            function contain(value = '') {
            
              if (includeDomains.length === 0 && excludeDomains.length === 0) return true;

              if (includeDomains.length > 0 && excludeDomains.length === 0) {
                return includeDomains.some(s => value.includes(s));
              }

              if (includeDomains.length === 0 && excludeDomains.length > 0) {
                return !excludeDomains.some(s => value.includes(s));
              }

              if (includeDomains.length > 0 && excludeDomains.length > 0) {

                return includeDomains.some(s => value.includes(s)) && !excludeDomains.some(s => value.includes(s));
              }
      
              return true;
            }
      
            function getAppId() {
              var pathname = location.pathname;

              if (pmmAppIds.length === 0) return appid;
              var curPmmAppIdInfo = pmmAppIds.find(pmm => pathname.startsWith(pmm.activeRule));
              if (curPmmAppIdInfo) {
                return curPmmAppIdInfo.appId;
              }

              return appid;
            }
        
            function pmmFrontError(e, data = {}) {
              var type = event.type, target = event.target;

              if (type !== 'error' || !target || target === window) return;
              var tagName = target.tagName, nodeName = target.nodeName, localName = target.localName;
              var resourceType = (tagName || nodeName || localName || '').toLowerCase();
              if (!resourceType) return;

              if (['img', 'script', 'link'].includes(resourceType)) {
                var now = Date.now(), nows = Math.floor(now / 1000) + '',
                attribute = { script: 'src', link: 'href', img: 'src' }[resourceType],
                value = attribute ? e.target.getAttribute(attribute) : '';
                if (!value) return;
                if (!contain(value)) return;
                var elementA = document.createElement('a'),
                href = ((elementA.href = value), elementA.href),
                hideClassName = false,
                initData = {
                  category: 5,
                  type: 501, 
                  timestamp: now,
                  tags: {
                    network: network + '',
                    url: href,
                    conn: '1', 
                    errorCode: "",
                    errorMsg: e.message
                      ? e.message + ',' + JSON.stringify((e.error || {}).stack)
                      : resourceType + '[' + attribute + '] load error', 
                    page: e.filename
                      ? e.filename + ':' + e.colno + ':' + e.lineno
                      : (e.composedPath ? e.composedPath() : [])
                          .map(function(el, i) {
                            var msg = el.nodeName
                            if (!hideClassName && el.className && i > 0) {
                              msg = msg + '.' + el.className
                              hideClassName = true
                            }
                            return i > 0 ? msg : el.outerHTML
                          })
                          .join(' < '), 
                    pageUrl: location.href,
                    packageType: '0',
                    mmrId: uuidv4(),
                    eventTime: nows, 
                    reportTime: nows,
                    iris_context_env: iris_context_env
                  },
                  extras: getPerformance((window.performance && performance.getEntriesByName(href)[0]) || {})
                }
                try {
                  data = Object.assign(initData, data, {
                    tags: Object.assign(initData.tags, data.tags)
                  })
                } catch (e) {}
                var curAppId = getAppId();
                navigator.sendBeacon(
                  origin + '/pmm/api/pmm/front_err',
                  JSON.stringify({
                   
                    report_time_ms: now,
                    rand_num: random,
                    crc32: crc32(now + '-' + random),
                    biz_side: 'supply',
                    app: curAppId,
                    common_tags: commonTags,
                    datas: [data]
                  })
                )
              }
            }
      
            window.__BGB_PMM_FRONT_ERROR_LOG__ = pmmFrontError
      
            window.addEventListener(
              'error',
              pmmFrontError,
              true
            )
          } catch (e) {}
        })()</script><script crossorigin="anonymous" defer="defer" src="https://bstatic.kwcdn.com/static/temu-sca-container/342.2fefe74c.js"></script><script crossorigin="anonymous" defer="defer" src="https://bstatic.kwcdn.com/static/temu-sca-container/main.a9651195.js"></script><link href="https://bstatic.kwcdn.com/static/temu-sca-container/342.cf140ea1f1fc4d01231e.css" rel="stylesheet"><link href="https://bstatic.kwcdn.com/static/temu-sca-container/main.6fb56afe68a80940815f.css" rel="stylesheet"></head><body><noscript>You need to enable JavaScript to run this app.</noscript><div id="sca-container-root"></div></body></html>