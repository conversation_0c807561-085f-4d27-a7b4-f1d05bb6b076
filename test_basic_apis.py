#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import os
from datetime import datetime

class BasicAPITester:
    def __init__(self):
        # 禁用代理
        os.environ.pop('HTTP_PROXY', None)
        os.environ.pop('HTTPS_PROXY', None)
        os.environ.pop('http_proxy', None)
        os.environ.pop('https_proxy', None)
        
        self.session = requests.Session()
        proxies = {'http': None, 'https': None}
        self.session.proxies.update(proxies)
        self.session.trust_env = False
        
        # 设置cookie
        cookie_string = "api_uid=Ct4kjmiNlzU98QBGDn3oAg==; _nano_fp=Xpmyn0golpC8XqT8X9_v~JX55DvIt6xeNJtDofAJ; _bee=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; njrpl=wUNSsMGdYpaRQjStoDpvD2LXVOOk6ap3; dilx=MchfnouEwE7NM0Un4s5iO; hfsc=L3yPfIgw4Dbw15/MeQ==; seller_temp=N_eyJ0IjoiUXlzUVZhMHlIMTcxb1hFemhoUlpMWlA5dkVoUzJoRlFiSjZxRTJOYWVldnFjZWt1aGU2cTV4VWsrY0ZqUWdJU2t5dEI1RVJkUjdXT0xJc21rWmhjVHc9PSIsInYiOjEsInMiOjEwMDAxLCJ1IjoyNDI3ODU0ODg1MTkwMn0=; mallid=634418224371052"
        
        # 解析cookie字符串
        for cookie in cookie_string.split('; '):
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                self.session.cookies.set(name, value, domain='.temu.com')
                self.session.cookies.set(name, value, domain='.agentseller.temu.com')
        
        # 设置基础请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Origin': 'https://agentseller.temu.com',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Content-Type': 'application/json'
        })
        
        self.successful_apis = []
    
    def test_api_with_referer(self, endpoint, referer, data=None):
        """使用指定的referer测试API"""
        self.session.headers['Referer'] = referer
        
        try:
            url = f"https://agentseller.temu.com{endpoint}"
            response = self.session.post(url, json=data or {}, timeout=30)
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                if 'application/json' in content_type:
                    try:
                        result = response.json()
                        return True, result
                    except json.JSONDecodeError:
                        return False, "Invalid JSON"
            
            return False, f"Status: {response.status_code}"
            
        except Exception as e:
            return False, str(e)
    
    def test_basic_info_apis(self):
        """测试基础信息API"""
        print("🏪 测试基础信息API...")
        
        # 不同页面的referer
        referers = [
            'https://agentseller.temu.com/',
            'https://agentseller.temu.com/dashboard',
            'https://agentseller.temu.com/account',
            'https://agentseller.temu.com/store',
            'https://agentseller.temu.com/product',
            'https://agentseller.temu.com/order',
            'https://agentseller.temu.com/newon/product-select'
        ]
        
        # 基础API端点
        basic_endpoints = [
            '/quick/merchant/pop/query',  # 已知有效
            '/quick/merchant/info',
            '/quick/merchant/profile',
            '/quick/account/info',
            '/quick/user/info',
            '/quick/store/info',
            '/quick/dashboard/data',
            '/quick/overview/data',
            '/api/account/info',
            '/api/user/info',
            '/api/merchant/info',
            '/api/store/info'
        ]
        
        for endpoint in basic_endpoints:
            print(f"\n  测试: {endpoint}")
            
            for referer in referers:
                success, result = self.test_api_with_referer(endpoint, referer)
                
                if success:
                    print(f"    ✅ 成功! (Referer: {referer})")
                    print(f"    响应: {json.dumps(result, ensure_ascii=False, indent=6)}")
                    
                    self.successful_apis.append({
                        'endpoint': endpoint,
                        'referer': referer,
                        'data': result,
                        'timestamp': datetime.now().isoformat()
                    })
                    break  # 找到有效的referer就停止
            else:
                print(f"    ❌ 所有referer都失败")
    
    def test_menu_and_navigation_apis(self):
        """测试菜单和导航相关API"""
        print(f"\n📋 测试菜单和导航API...")
        
        menu_endpoints = [
            '/api/menu/list',
            '/api/navigation/list',
            '/api/sidebar/list',
            '/quick/menu/list',
            '/quick/navigation/list',
            '/api/bg-ladyfish/mms/menu/list',
            '/api/bg-ladyfish/mms/navigation/list'
        ]
        
        referer = 'https://agentseller.temu.com/'
        
        for endpoint in menu_endpoints:
            print(f"  测试: {endpoint}")
            success, result = self.test_api_with_referer(endpoint, referer)
            
            if success:
                print(f"    ✅ 成功!")
                print(f"    响应: {json.dumps(result, ensure_ascii=False, indent=6)}")
                
                self.successful_apis.append({
                    'endpoint': endpoint,
                    'referer': referer,
                    'data': result,
                    'timestamp': datetime.now().isoformat()
                })
            else:
                print(f"    ❌ 失败: {result}")
    
    def test_notification_apis(self):
        """测试通知相关API"""
        print(f"\n🔔 测试通知相关API...")
        
        notification_endpoints = [
            '/quick/notification/list',
            '/quick/message/list',
            '/quick/alert/list',
            '/api/notification/list',
            '/api/message/list',
            '/api/alert/list'
        ]
        
        referer = 'https://agentseller.temu.com/'
        
        for endpoint in notification_endpoints:
            print(f"  测试: {endpoint}")
            
            # 尝试不同的参数
            param_sets = [
                {},
                {'page': 1, 'size': 10},
                {'status': 'unread'},
                {'type': 'all'}
            ]
            
            for params in param_sets:
                success, result = self.test_api_with_referer(endpoint, referer, params)
                
                if success:
                    print(f"    ✅ 成功! 参数: {params}")
                    print(f"    响应: {json.dumps(result, ensure_ascii=False, indent=6)}")
                    
                    self.successful_apis.append({
                        'endpoint': endpoint,
                        'referer': referer,
                        'params': params,
                        'data': result,
                        'timestamp': datetime.now().isoformat()
                    })
                    break
            else:
                print(f"    ❌ 所有参数都失败")
    
    def test_status_and_health_apis(self):
        """测试状态和健康检查API"""
        print(f"\n💓 测试状态和健康检查API...")
        
        status_endpoints = [
            '/api/health',
            '/api/status',
            '/api/ping',
            '/api/version',
            '/quick/health',
            '/quick/status',
            '/quick/ping'
        ]
        
        referer = 'https://agentseller.temu.com/'
        
        for endpoint in status_endpoints:
            print(f"  测试: {endpoint}")
            success, result = self.test_api_with_referer(endpoint, referer)
            
            if success:
                print(f"    ✅ 成功!")
                print(f"    响应: {json.dumps(result, ensure_ascii=False, indent=6)}")
                
                self.successful_apis.append({
                    'endpoint': endpoint,
                    'referer': referer,
                    'data': result,
                    'timestamp': datetime.now().isoformat()
                })
            else:
                print(f"    ❌ 失败: {result}")
    
    def save_results(self):
        """保存结果"""
        if not self.successful_apis:
            print("\n❌ 没有发现有效的API")
            return None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"basic_apis_success_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.successful_apis, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 结果已保存到: {filename}")
        return filename

def main():
    print("🔍 测试基础API端点...")
    
    tester = BasicAPITester()
    
    # 测试不同类型的API
    tester.test_basic_info_apis()
    tester.test_menu_and_navigation_apis()
    tester.test_notification_apis()
    tester.test_status_and_health_apis()
    
    # 保存结果
    if tester.successful_apis:
        filename = tester.save_results()
        
        print(f"\n🎉 测试完成!")
        print(f"发现 {len(tester.successful_apis)} 个有效的API:")
        
        for api in tester.successful_apis:
            endpoint = api['endpoint']
            referer = api.get('referer', '')
            params = api.get('params', '')
            if params:
                print(f"  ✅ {endpoint} (参数: {params})")
            else:
                print(f"  ✅ {endpoint}")
        
        if filename:
            print(f"\n📁 结果文件: {filename}")
    else:
        print(f"\n❌ 没有发现有效的API")
        print("建议查看浏览器网络面板中的实际API调用")

if __name__ == "__main__":
    main()
